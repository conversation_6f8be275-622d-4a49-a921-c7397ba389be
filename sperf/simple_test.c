#include <stdio.h>
#include <stdlib.h>
#include <unistd.h>
#include <sys/wait.h>

// 简单测试程序，验证基本的fork和execve功能
int main() {
    printf("=== 简单测试：验证fork和execve功能 ===\n");
    
    // 测试1：基本的fork和execve
    pid_t pid = fork();
    if (pid == 0) {
        // 子进程：执行echo命令
        char* argv[] = {"echo", "Hello from child process", NULL};
        extern char **environ;
        execve("/bin/echo", argv, environ);
        perror("execve failed");
        exit(1);
    } else if (pid > 0) {
        // 父进程：等待子进程
        int status;
        waitpid(pid, &status, 0);
        printf("子进程结束，状态: %d\n", WEXITSTATUS(status));
    } else {
        perror("fork failed");
        return 1;
    }
    
    printf("\n=== 测试完成 ===\n");
    return 0;
}
