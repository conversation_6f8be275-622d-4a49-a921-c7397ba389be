#!/bin/bash

echo "=== 编译和测试sperf第三四步功能 ==="

# 检查是否在sperf目录
if [ ! -f "sperf.c" ]; then
    echo "错误: 请在sperf目录下运行此脚本"
    exit 1
fi

# 编译简单测试程序
echo "1. 编译简单测试程序..."
gcc -o simple_test simple_test.c
if [ $? -eq 0 ]; then
    echo "简单测试程序编译成功"
    echo "运行简单测试..."
    ./simple_test
else
    echo "简单测试程序编译失败"
    exit 1
fi

echo -e "\n2. 编译sperf程序..."
make clean
make

if [ $? -ne 0 ]; then
    echo "sperf编译失败！"
    exit 1
fi

echo -e "\n3. 检查strace是否可用..."
which strace > /dev/null
if [ $? -ne 0 ]; then
    echo "警告: 未找到strace，请安装："
    echo "sudo apt-get install strace"
    exit 1
fi

echo -e "\n4. 测试sperf基本功能..."

echo -e "\n测试4.1: echo命令"
./sperf-64 echo "test message"

echo -e "\n测试4.2: ls命令"
./sperf-64 ls

echo -e "\n测试4.3: date命令"
./sperf-64 date

echo -e "\n=== 所有测试完成 ==="
