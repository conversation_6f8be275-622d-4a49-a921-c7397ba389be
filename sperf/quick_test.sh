#!/bin/bash

echo "=== sperf 快速验证测试 ==="

# 编译
echo "编译程序..."
make clean && make
if [ $? -ne 0 ]; then
    echo "编译失败！"
    exit 1
fi

# 检查strace
which strace > /dev/null
if [ $? -ne 0 ]; then
    echo "错误: 未找到strace，请安装: sudo apt-get install strace"
    exit 1
fi

echo -e "\n快速功能验证："

echo -e "\n1. 测试基本功能 - echo命令"
./sperf-64 echo "hello world"

echo -e "\n2. 测试文件操作 - cat命令"
./sperf-64 cat /etc/hostname

echo -e "\n3. 测试目录操作 - ls命令"
./sperf-64 ls /tmp

echo -e "\n4. 测试长时间运行 - sleep命令"
./sperf-64 sleep 1

echo -e "\n5. 测试错误处理 - 不存在的命令"
./sperf-64 nonexistent_command_test

echo -e "\n=== 快速测试完成 ==="
echo "如果看到系统调用统计输出（格式如 'write (50%)'），说明程序工作正常！"
