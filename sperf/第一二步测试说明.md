# sperf 第一步和第二步测试说明

## 编译方法
```bash
cd sperf
make clean
make
```

## 第一步测试：命令行参数解析和PATH搜索

### 测试用例1：查找普通命令
```bash
./sperf-64 ls
```
**期望输出：**
```
正在查找命令: ls
找到可执行文件: /bin/ls (或 /usr/bin/ls)
父进程等待子进程(PID: xxxx)结束...
[ls的正常输出]
子进程正常结束，退出码: 0
```

### 测试用例2：使用绝对路径
```bash
./sperf-64 /bin/ls
```
**期望输出：**
```
正在查找命令: /bin/ls
找到可执行文件: /bin/ls
父进程等待子进程(PID: xxxx)结束...
[ls的正常输出]
子进程正常结束，退出码: 0
```

### 测试用例3：查找不存在的命令
```bash
./sperf-64 nonexistent_command
```
**期望输出：**
```
正在查找命令: nonexistent_command
错误: 找不到命令 'nonexistent_command'
```

### 测试用例4：无参数调用
```bash
./sperf-64
```
**期望输出：**
```
使用方法: ./sperf-64 COMMAND [ARG]...
启动COMMAND程序并统计其系统调用的占用时间

示例:
  ./sperf-64 ls /tmp
  ./sperf-64 echo "hello world"
```

## 第二步测试：基本进程创建和execve调用

### 测试用例5：执行echo命令
```bash
./sperf-64 echo "hello world"
```
**期望输出：**
```
正在查找命令: echo
找到可执行文件: /bin/echo (或其他路径)
父进程等待子进程(PID: xxxx)结束...
子进程开始执行: /bin/echo
hello world
子进程正常结束，退出码: 0
```

### 测试用例6：执行ls命令
```bash
./sperf-64 ls /tmp
```
**期望输出：**
```
正在查找命令: ls
找到可执行文件: /bin/ls
父进程等待子进程(PID: xxxx)结束...
子进程开始执行: /bin/ls
[/tmp目录的内容列表]
子进程正常结束，退出码: 0
```

### 测试用例7：带多个参数的命令
```bash
./sperf-64 echo "参数1" "参数2" "参数3"
```
**期望输出：**
```
正在查找命令: echo
找到可执行文件: /bin/echo
父进程等待子进程(PID: xxxx)结束...
子进程开始执行: /bin/echo
参数1 参数2 参数3
子进程正常结束，退出码: 0
```

## 验证要点

### 第一步验证要点：
1. **PATH搜索功能**：能正确在PATH环境变量中搜索可执行文件
2. **绝对路径处理**：能正确处理以'/'开头的绝对路径
3. **错误处理**：找不到命令时能正确报错
4. **参数验证**：无参数时显示使用说明

### 第二步验证要点：
1. **进程创建**：能成功使用fork()创建子进程
2. **execve调用**：能正确使用execve执行目标程序（不使用其他exec函数）
3. **参数传递**：能正确传递命令行参数给目标程序
4. **进程等待**：父进程能正确等待子进程结束
5. **状态获取**：能正确获取子进程的退出状态

## 可能遇到的问题

1. **编译错误**：检查是否包含了所有必要的头文件
2. **PATH问题**：确保PATH环境变量设置正确
3. **权限问题**：确保目标文件有执行权限
4. **内存泄漏**：注意释放malloc分配的内存

## 下一步计划

完成第一步和第二步测试后，将继续实现：
- 第三步：集成strace进行系统调用追踪
- 第四步：管道通信和strace输出捕获
- 后续步骤...
