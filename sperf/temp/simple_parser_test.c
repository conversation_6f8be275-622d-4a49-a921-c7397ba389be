#include <stdio.h>
#include <stdlib.h>
#include <string.h>

// 简化的解析器测试，不使用正则表达式
int simple_parse_line(const char* line) {
    printf("测试解析: %s\n", line);
    
    // 复制字符串用于处理
    char* line_copy = strdup(line);
    if (!line_copy) return 0;
    
    // 查找第一个'('来确定系统调用名称
    char* paren_pos = strchr(line_copy, '(');
    if (!paren_pos) {
        printf("  失败: 找不到'('\n");
        free(line_copy);
        return 0;
    }
    
    // 提取系统调用名称
    *paren_pos = '\0';
    printf("  系统调用名称: %s\n", line_copy);
    
    // 查找时间信息 <数字.数字>
    char* time_start = strrchr(line, '<');
    char* time_end = strrchr(line, '>');
    
    if (!time_start || !time_end || time_start >= time_end) {
        printf("  失败: 找不到时间信息\n");
        free(line_copy);
        return 0;
    }
    
    // 提取时间值
    char time_str[32];
    int time_len = time_end - time_start - 1;
    if (time_len >= sizeof(time_str) || time_len <= 0) {
        printf("  失败: 时间格式错误\n");
        free(line_copy);
        return 0;
    }
    
    strncpy(time_str, time_start + 1, time_len);
    time_str[time_len] = '\0';
    double time_value = atof(time_str);
    
    printf("  时间: %s (%.6f秒)\n", time_str, time_value);
    printf("  成功解析\n");
    
    free(line_copy);
    return 1;
}

int main() {
    printf("=== 简化的strace输出解析器测试 ===\n\n");
    
    // 测试用例
    const char* test_lines[] = {
        "write(1, \"hello\\n\", 6) = 6 <0.000050>",
        "openat(AT_FDCWD, \"/etc/passwd\", O_RDONLY) = 3 <0.000100>",
        "read(3, \"root:x:0:0:root:/root:/bin/bash\\n\"..., 4096) = 1024 <0.000023>",
        "close(3) = 0 <0.000015>",
        "execve(\"/bin/ls\", [\"ls\"], 0x7fff... /* 23 vars */) = 0 <0.000123>",
        "brk(NULL) = 0x55c642aa9000 <0.000008>",
        "mmap(NULL, 8192, PROT_READ|PROT_WRITE, MAP_PRIVATE|MAP_ANONYMOUS, -1, 0) = 0x7f8b8c8a1000 <0.000012>",
        "getdents64(3, /* 目录项 */, 32768) = 1024 <0.000234>",
        "lstat(\"/tmp/file\", {st_mode=S_IFREG|0644, st_size=1024, ...}) = 0 <0.000045>",
        "这是一个无效的行",
        "另一个无效行 without proper format",
        NULL
    };
    
    int success_count = 0;
    int total_count = 0;
    
    for (int i = 0; test_lines[i] != NULL; i++) {
        if (simple_parse_line(test_lines[i])) {
            success_count++;
        }
        total_count++;
        printf("\n");
    }
    
    printf("=== 测试结果 ===\n");
    printf("总测试数: %d\n", total_count);
    printf("成功解析: %d\n", success_count);
    printf("失败解析: %d\n", total_count - success_count);
    printf("成功率: %.1f%%\n", (double)success_count / total_count * 100);
    
    return 0;
}
