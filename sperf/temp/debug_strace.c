#include <stdio.h>
#include <stdlib.h>
#include <string.h>
#include <unistd.h>
#include <sys/wait.h>

// 调试版本：测试strace调用
int main(int argc, char *argv[]) {
    if (argc < 2) {
        printf("用法: %s COMMAND [ARG]...\n", argv[0]);
        return 1;
    }
    
    printf("=== 调试strace调用 ===\n");
    printf("原始参数:\n");
    for (int i = 0; i < argc; i++) {
        printf("  argv[%d] = %s\n", i, argv[i]);
    }
    
    // 简单测试：直接调用strace
    pid_t pid = fork();
    if (pid == 0) {
        // 子进程：构造strace命令
        printf("子进程：构造strace命令\n");
        
        // 简单的strace调用：strace -T /bin/echo hello
        char* strace_argv[] = {
            "strace",
            "-T",
            "/bin/echo",
            "hello",
            "from",
            "strace",
            NULL
        };
        
        printf("执行命令: ");
        for (int i = 0; strace_argv[i] != NULL; i++) {
            printf("%s ", strace_argv[i]);
        }
        printf("\n");
        
        extern char **environ;
        execve("/usr/bin/strace", strace_argv, environ);
        perror("execve失败");
        exit(1);
    } else if (pid > 0) {
        // 父进程：等待子进程
        int status;
        waitpid(pid, &status, 0);
        printf("子进程结束，状态: %d\n", WEXITSTATUS(status));
    } else {
        perror("fork失败");
        return 1;
    }
    
    return 0;
}
