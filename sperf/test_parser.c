#include <stdio.h>
#include <stdlib.h>
#include <string.h>
#include <regex.h>

// 测试strace输出解析功能
int test_parse_line(const char* line) {
    printf("测试解析: %s\n", line);
    
    regex_t regex;
    regmatch_t matches[3];
    
    // 正则表达式：匹配系统调用名称和时间
    const char* pattern = "^([a-zA-Z_][a-zA-Z0-9_]*)\\(.*\\) = .* <([0-9]+\\.[0-9]+)>";
    
    if (regcomp(&regex, pattern, REG_EXTENDED) != 0) {
        printf("  错误: 正则表达式编译失败\n");
        return 0;
    }
    
    if (regexec(&regex, line, 3, matches, 0) == 0) {
        // 提取系统调用名称
        int name_len = matches[1].rm_eo - matches[1].rm_so;
        char syscall_name[64];
        strncpy(syscall_name, line + matches[1].rm_so, name_len);
        syscall_name[name_len] = '\0';
        
        // 提取时间
        int time_len = matches[2].rm_eo - matches[2].rm_so;
        char time_str[32];
        strncpy(time_str, line + matches[2].rm_so, time_len);
        time_str[time_len] = '\0';
        double time_value = atof(time_str);
        
        printf("  成功: 系统调用=%s, 时间=%f\n", syscall_name, time_value);
        regfree(&regex);
        return 1;
    } else {
        printf("  失败: 无法匹配\n");
    }
    
    regfree(&regex);
    return 0;
}

int main() {
    printf("=== 测试strace输出解析器 ===\n\n");
    
    // 测试用例
    const char* test_lines[] = {
        "write(1, \"hello\\n\", 6) = 6 <0.000050>",
        "openat(AT_FDCWD, \"/etc/passwd\", O_RDONLY) = 3 <0.000100>",
        "read(3, \"root:x:0:0:root:/root:/bin/bash\\n\"..., 4096) = 1024 <0.000023>",
        "close(3) = 0 <0.000015>",
        "execve(\"/bin/ls\", [\"ls\"], 0x7fff... /* 23 vars */) = 0 <0.000123>",
        "brk(NULL) = 0x55c642aa9000 <0.000008>",
        "mmap(NULL, 8192, PROT_READ|PROT_WRITE, MAP_PRIVATE|MAP_ANONYMOUS, -1, 0) = 0x7f8b8c8a1000 <0.000012>",
        "这是一个无效的行",
        "另一个无效行 without proper format",
        NULL
    };
    
    int success_count = 0;
    int total_count = 0;
    
    for (int i = 0; test_lines[i] != NULL; i++) {
        if (test_parse_line(test_lines[i])) {
            success_count++;
        }
        total_count++;
        printf("\n");
    }
    
    printf("=== 测试结果 ===\n");
    printf("总测试数: %d\n", total_count);
    printf("成功解析: %d\n", success_count);
    printf("失败解析: %d\n", total_count - success_count);
    
    return 0;
}
