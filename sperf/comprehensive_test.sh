#!/bin/bash

echo "=== sperf 第四步和第五步综合测试 ==="

# 检查是否在sperf目录
if [ ! -f "sperf.c" ]; then
    echo "错误: 请在sperf目录下运行此脚本"
    exit 1
fi

# 1. 编译解析器测试程序
echo "1. 编译解析器测试程序..."
gcc -o test_parser test_parser.c
if [ $? -eq 0 ]; then
    echo "解析器测试程序编译成功"
    echo "运行解析器测试..."
    ./test_parser
    echo ""
else
    echo "解析器测试程序编译失败"
    exit 1
fi

# 2. 编译sperf程序
echo "2. 编译sperf程序..."
make clean
make

if [ $? -ne 0 ]; then
    echo "sperf编译失败！"
    exit 1
fi

# 3. 检查strace
echo "3. 检查strace是否可用..."
which strace > /dev/null
if [ $? -ne 0 ]; then
    echo "警告: 未找到strace，请安装："
    echo "sudo apt-get install strace"
    exit 1
fi

# 4. 基本功能测试
echo "4. 基本功能测试..."

echo "4.1 测试echo命令（短时间）"
./sperf-64 echo "test message"
echo ""

echo "4.2 测试date命令"
./sperf-64 date
echo ""

echo "4.3 测试ls命令"
./sperf-64 ls /tmp
echo ""

# 5. 长时间运行测试
echo "5. 长时间运行测试（定时输出）..."

echo "5.1 测试sleep命令（2秒）"
./sperf-64 sleep 2
echo ""

# 6. 复杂命令测试
echo "6. 复杂命令测试..."

echo "6.1 测试cat命令"
./sperf-64 cat /etc/hostname
echo ""

echo "6.2 测试find命令（限时3秒）"
timeout 3 ./sperf-64 find /tmp -name "*.tmp" 2>/dev/null || echo "find命令超时结束"
echo ""

echo "=== 所有测试完成 ==="
echo ""
echo "验证要点："
echo "1. 应该看到'解析成功'的输出"
echo "2. 应该看到系统调用统计信息"
echo "3. 长时间命令应该有定时输出"
echo "4. 最终应该有完整的统计结果"
echo "5. 程序应该正常退出"
