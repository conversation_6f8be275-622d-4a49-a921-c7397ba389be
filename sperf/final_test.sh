#!/bin/bash

echo "=== sperf 完整功能测试 ==="

# 检查是否在sperf目录
if [ ! -f "sperf.c" ]; then
    echo "错误: 请在sperf目录下运行此脚本"
    exit 1
fi

# 编译程序
echo "1. 编译sperf程序..."
make clean
make

if [ $? -ne 0 ]; then
    echo "编译失败！"
    exit 1
fi

# 检查strace
echo "2. 检查strace是否可用..."
which strace > /dev/null
if [ $? -ne 0 ]; then
    echo "警告: 未找到strace，请安装："
    echo "sudo apt-get install strace"
    exit 1
fi

echo "3. 基本功能测试..."

# 测试错误情况
echo "3.1 测试无参数调用"
./sperf-64
echo ""

echo "3.2 测试不存在的命令"
./sperf-64 nonexistent_command_12345
echo ""

echo "3.3 测试不可执行文件"
./sperf-64 /etc/passwd 2>/dev/null || echo "正确处理了不可执行文件"
echo ""

# 测试基本功能
echo "4. 基本系统调用测试..."

echo "4.1 测试echo命令"
./sperf-64 echo "hello world"
echo ""

echo "4.2 测试date命令"
./sperf-64 date
echo ""

echo "4.3 测试whoami命令"
./sperf-64 whoami
echo ""

# 测试复杂命令
echo "5. 复杂系统调用测试..."

echo "5.1 测试ls命令"
./sperf-64 ls /tmp
echo ""

echo "5.2 测试cat命令"
./sperf-64 cat /etc/hostname
echo ""

echo "5.3 测试带参数的命令"
./sperf-64 echo "参数1" "参数2" "参数3"
echo ""

# 测试长时间运行命令
echo "6. 长时间运行测试（定时输出）..."

echo "6.1 测试sleep命令（1秒）"
./sperf-64 sleep 1
echo ""

echo "6.2 测试find命令（限时2秒）"
timeout 2 ./sperf-64 find /tmp -name "*.tmp" 2>/dev/null || echo "find命令超时结束"
echo ""

# 测试绝对路径
echo "7. 绝对路径测试..."

echo "7.1 测试绝对路径echo"
./sperf-64 /bin/echo "absolute path test"
echo ""

echo "7.2 测试绝对路径ls"
./sperf-64 /bin/ls /tmp
echo ""

echo "=== 所有测试完成 ==="
echo ""
echo "验证要点："
echo "1. 错误情况应该有适当的错误信息"
echo "2. 短时间命令应该输出最终统计结果"
echo "3. 长时间命令应该有定时输出（每100ms）"
echo "4. 输出格式应该是: syscall_name (XX%)"
echo "5. 每次统计后应该有80个不可见的\\0字符"
echo "6. 程序应该正常退出，不会hang住"
