第三步和第四步手动测试指令（请在sperf目录下执行）：

前置检查：
1. 检查strace是否安装：
   which strace
   如果没有安装，执行：sudo apt-get install strace

编译和基本测试：
2. 编译程序：
   make clean && make

3. 测试strace集成 - 简单命令：
   ./sperf-64 echo "hello"
   
4. 测试strace集成 - ls命令：
   ./sperf-64 ls

5. 测试管道通信 - 带参数：
   ./sperf-64 echo "hello world"

6. 测试管道通信 - 目录列表：
   ./sperf-64 ls /tmp

7. 测试实时输出 - 短暂延迟：
   ./sperf-64 sleep 1

8. 测试复杂命令：
   ./sperf-64 date
   ./sperf-64 whoami
   ./sperf-64 pwd

预期结果验证：
- 应该能看到"找到strace: /usr/bin/strace"的输出
- 应该能看到"管道创建成功"的输出
- 应该能看到"=== strace输出 ==="的标题
- 应该能看到strace的详细输出，包含：
  * execve系统调用
  * 各种系统调用及其时间（如 <0.000123>）
  * write、read、openat等常见系统调用
- 应该能看到"子进程结束"和"总共读取了 X 行"的统计
- 程序应该正常退出，不会hang住

故障排除：
- 如果看不到strace输出，检查strace是否正确安装
- 如果程序hang住，可能是管道处理有问题
- 如果看到权限错误，可能需要调整strace权限设置
