输入：./sperf-64 ls
期望：能找到/bin/ls或/usr/bin/ls的完整路径

输入：./sperf-64 /bin/ls
期望：直接使用/bin/ls路径

输入：./sperf-64 nonexistent_command
期望：报错，找不到命令



输入：./sperf-64 echo "hello world"
期望：能正确执行echo命令并输出"hello world"

输入：./sperf-64 ls /tmp
期望：能正确执行ls命令并列出/tmp目录内容





输入：./sperf-64 echo "test"
期望：能看到strace的输出，包含execve、write等系统调用

输入：./sperf-64 ls
期望：能看到strace的输出，包含openat、getdents等系统调用





输入：./sperf-64 echo "test"
期望：父进程能读取到完整的strace输出内容

输入：./sperf-64 sleep 1
期望：父进程能实时读取到strace的输出




输入strace行：write(1, "hello\n", 6) = 6 <0.000050>
期望解析结果：syscall_name="write", time=0.000050

输入strace行：openat(AT_FDCWD, "/etc/passwd", O_RDONLY) = 3 <0.000100>
期望解析结果：syscall_name="openat", time=0.000100






假设统计数据：
write: 0.001000秒
read: 0.002000秒
openat: 0.000500秒
总时间: 0.003500秒

期望计算结果：
read: 57% (2000/3500)
write: 29% (1000/3500)
openat: 14% (500/3500)






输入：./sperf-64 echo "test"
期望输出格式：
write (50%)
execve (30%)
brk (20%)
\0\0\0...(80个\0字符)

输入：./sperf-64 find /tmp
期望：每100ms左右输出一次统计结果，直到find命令结束





输入：./sperf-64 echo "test"
期望：echo结束后，sperf也立即结束

输入：./sperf-64 sleep 2
期望：sleep 2秒后结束，sperf也随之结束




输入：./sperf-64 nonexistent_command
期望：输出错误信息并退出

输入：./sperf-64（无参数）
期望：输出使用说明并退出

输入：./sperf-64 /dev/null
期望：能正确处理无法执行的文件





输入：./sperf-64 ls /
期望输出示例：
getdents (45%)
lstat (30%)
openat (15%)
write (7%)
close (3%)
\0\0\0...(80个\0字符)

输入：./sperf-64 find /tmp
期望：每100ms输出一次统计，显示find命令的系统调用分布

输入：./sperf-64 cat /etc/passwd
期望：显示read、write等系统调用的时间分布
