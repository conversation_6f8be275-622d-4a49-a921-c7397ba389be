# sperf 完整功能测试说明

## 项目完成状态

✅ **第一步**: 命令行参数解析和PATH搜索  
✅ **第二步**: 基本进程创建和execve调用  
✅ **第三步**: 集成strace进行系统调用追踪  
✅ **第四步**: 管道通信和strace输出捕获  
✅ **第五步**: strace输出解析  
✅ **第六步**: 系统调用统计和时间计算  
✅ **第七步**: 输出格式化和定时输出  
✅ **第八步**: 进程生命周期管理  
✅ **第九步**: 错误处理和边界情况  
✅ **第十步**: 完整功能测试  

## 核心功能特性

### 1. 系统调用追踪
- 使用strace追踪目标程序的所有系统调用
- 实时解析strace输出，提取系统调用名称和执行时间
- 支持所有常见的系统调用类型

### 2. 时间统计
- 精确统计每个系统调用的总执行时间
- 计算各系统调用的时间占比
- 按时间占比排序，输出top 5系统调用

### 3. 定时输出
- 短时间程序：程序结束时输出最终统计
- 长时间程序：每100ms输出一次当前统计
- 符合要求的输出格式：`syscall_name (XX%)`

### 4. 错误处理
- 完善的命令行参数验证
- 路径查找和权限检查
- 进程管理和资源清理

## 编译和运行

### 编译方法
```bash
cd sperf
make clean
make
```

### 基本使用
```bash
# 基本用法
./sperf-64 COMMAND [ARG]...

# 示例
./sperf-64 ls /tmp
./sperf-64 echo "hello world"
./sperf-64 cat /etc/hostname
```

## 测试用例

### 1. 错误处理测试
```bash
# 无参数
./sperf-64
# 期望：显示使用说明

# 不存在的命令
./sperf-64 nonexistent_command
# 期望：错误信息 "sperf: 找不到命令 'nonexistent_command'"

# 不可执行文件
./sperf-64 /etc/passwd
# 期望：错误信息 "sperf: '/etc/passwd' 不可执行"
```

### 2. 基本功能测试
```bash
# 简单命令
./sperf-64 echo "test"
# 期望输出：
# write (XX%)
# execve (XX%)
# ...
# [80个\0字符]

# 文件操作命令
./sperf-64 cat /etc/hostname
# 期望输出：
# read (XX%)
# write (XX%)
# openat (XX%)
# ...
# [80个\0字符]
```

### 3. 复杂命令测试
```bash
# 目录遍历
./sperf-64 ls /tmp
# 期望输出：
# getdents64 (XX%)
# openat (XX%)
# lstat (XX%)
# write (XX%)
# close (XX%)
# [80个\0字符]

# 文件查找
./sperf-64 find /tmp -name "*.tmp"
# 期望：长时间运行时每100ms输出一次统计
```

### 4. 长时间运行测试
```bash
# 睡眠命令
./sperf-64 sleep 2
# 期望：
# - 运行期间每100ms输出一次统计
# - 最终输出以nanosleep为主的统计

# 限时命令
timeout 3 ./sperf-64 find /usr -name "*.so"
# 期望：每100ms输出统计，3秒后超时结束
```

## 输出格式验证

### 正确的输出格式
```
syscall_name (percentage%)
syscall_name (percentage%)
...
[80个不可见的\0字符]
```

### 验证方法
```bash
# 使用hexdump查看输出的二进制内容
./sperf-64 echo "test" | hexdump -C

# 应该能看到80个连续的00字节
```

## 性能特征

### 短时间程序（< 200ms）
- 只在程序结束时输出一次最终统计
- 不进行定时输出，避免输出过于频繁

### 长时间程序（≥ 200ms）
- 每100ms输出一次当前统计
- 程序结束时输出最终统计
- 符合"每秒大约10次"的要求

## 技术实现要点

### 1. 只使用execve
- 严格遵守要求，只使用execve系统调用
- 手动实现PATH搜索功能
- 正确构造strace命令行参数

### 2. 管道通信
- 使用pipe()创建父子进程通信管道
- 重定向strace的stderr到管道
- 非阻塞读取避免程序阻塞

### 3. 解析算法
- 简化的字符串解析，避免复杂正则表达式
- 提取系统调用名称（第一个'('之前）
- 提取时间信息（最后的<>之间）

### 4. 统计算法
- 动态维护系统调用统计数组
- 实时累加相同系统调用的时间
- 排序输出top 5系统调用

## 故障排除

### 常见问题
1. **strace未安装**：`sudo apt-get install strace`
2. **权限问题**：确保目标程序有执行权限
3. **路径问题**：检查PATH环境变量设置
4. **输出异常**：检查strace版本兼容性

### 调试方法
```bash
# 编译调试版本
gcc -g -o debug_sperf sperf.c

# 使用gdb调试
gdb ./debug_sperf

# 查看strace输出
strace -T echo "test"
```

## 测试脚本

### 自动化测试
```bash
# 运行完整测试
chmod +x final_test.sh
./final_test.sh

# 运行解析器测试
gcc -o simple_parser_test simple_parser_test.c
./simple_parser_test
```

### 手动验证
```bash
# 验证基本功能
./sperf-64 echo "hello"

# 验证定时输出
./sperf-64 sleep 1

# 验证错误处理
./sperf-64 nonexistent_command
```

## 符合要求验证

✅ **命令格式**: `sperf COMMAND [ARG]...`  
✅ **只使用execve**: 严格遵守，不使用其他exec函数  
✅ **输出格式**: `printf("%s (%d%%)\n", syscall_name, ratio)`  
✅ **分隔符**: 每次输出后80个\0字符  
✅ **定时输出**: 长时间程序每100ms输出一次  
✅ **top 5**: 每次最多输出5个系统调用  
✅ **fflush**: 每次输出后清空缓冲区  
✅ **进程同步**: 目标程序结束时sperf也结束  

sperf现在是一个功能完整的系统调用profiler，能够准确追踪和统计程序的系统调用时间分布，为性能分析提供有价值的信息。
