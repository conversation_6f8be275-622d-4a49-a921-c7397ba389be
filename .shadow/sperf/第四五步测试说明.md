# sperf 第四步和第五步测试说明

## 新增功能

### 第四步：优化的管道通信
1. **定时输出机制**：每100ms输出一次统计结果
2. **实时解析**：边读取边解析strace输出
3. **非阻塞读取**：避免程序阻塞
4. **进程同步优化**：更好的子进程生命周期管理

### 第五步：strace输出解析
1. **正则表达式解析**：提取系统调用名称和时间
2. **统计数据结构**：维护每个系统调用的时间和次数
3. **排序和输出**：按时间占比排序，输出top 5
4. **格式化输出**：符合要求的输出格式

## 编译方法
```bash
cd sperf
make clean
make

# 编译解析器测试程序
gcc -o test_parser test_parser.c
```

## 第四步测试：优化的管道通信

### 测试用例1：短时间命令
```bash
./sperf-64 echo "hello world"
```
**期望输出：**
```
正在查找命令: echo
找到可执行文件: /bin/echo
找到strace: /usr/bin/strace
管道创建成功，读端: 3, 写端: 4
父进程开始读取strace输出(子进程PID: xxxx)...
构造的strace命令: strace -T /bin/echo hello world
解析成功: execve("/bin/echo", ["echo", "hello world"], ...) = 0 <0.000123>
解析成功: write(1, "hello world\n", 12) = 12 <0.000067>
...
子进程结束，最后读取剩余输出...

=== 最终系统调用统计 ===
write (45%)
execve (30%)
brk (15%)
close (10%)
\0\0\0...(80个\0字符)
strace正常结束，退出码: 0
总共读取了 XX 行strace输出
解析了 X 个不同的系统调用
总执行时间: 0.000XXX 秒
```

### 测试用例2：长时间运行命令
```bash
./sperf-64 sleep 2
```
**期望输出：**
```
正在查找命令: sleep
找到可执行文件: /bin/sleep
...
解析成功: execve("/bin/sleep", ["sleep", "2"], ...) = 0 <0.000123>

=== 系统调用统计 (0.1s) ===
execve (100%)
\0\0\0...(80个\0字符)

=== 系统调用统计 (0.2s) ===
execve (50%)
nanosleep (50%)
\0\0\0...(80个\0字符)

...（每100ms输出一次）

=== 最终系统调用统计 ===
nanosleep (95%)
execve (5%)
\0\0\0...(80个\0字符)
```

## 第五步测试：strace输出解析

### 测试用例3：解析器功能测试
```bash
./test_parser
```
**期望输出：**
```
=== 测试strace输出解析器 ===

测试解析: write(1, "hello\n", 6) = 6 <0.000050>
  成功: 系统调用=write, 时间=0.000050

测试解析: openat(AT_FDCWD, "/etc/passwd", O_RDONLY) = 3 <0.000100>
  成功: 系统调用=openat, 时间=0.000100

...

=== 测试结果 ===
总测试数: 9
成功解析: 7
失败解析: 2
```

### 测试用例4：复杂命令解析
```bash
./sperf-64 ls /tmp
```
**期望输出：**
```
...
解析成功: openat(AT_FDCWD, "/tmp", O_RDONLY|O_NONBLOCK|O_CLOEXEC|O_DIRECTORY) = 3 <0.000089>
解析成功: getdents64(3, /* 目录项 */, 32768) = 1024 <0.000234>
解析成功: write(1, "file1\nfile2\n", 12) = 12 <0.000045>
...

=== 最终系统调用统计 ===
getdents64 (40%)
openat (25%)
write (20%)
lstat (10%)
close (5%)
\0\0\0...(80个\0字符)
```

## 验证要点

### 第四步验证要点：
1. **定时输出**：长时间运行的命令应该每100ms输出一次统计
2. **实时解析**：能看到"解析成功"的输出
3. **非阻塞读取**：程序不会hang住
4. **最终统计**：程序结束时输出完整统计

### 第五步验证要点：
1. **正则表达式匹配**：能正确提取系统调用名称和时间
2. **统计累加**：相同系统调用的时间能正确累加
3. **排序输出**：按时间占比从高到低排序
4. **格式要求**：输出格式符合`syscall_name (XX%)`
5. **分隔符**：每次输出后有80个\0字符

## 关键技术实现

### 正则表达式解析：
```c
const char* pattern = "^([a-zA-Z_][a-zA-Z0-9_]*)\\(.*\\) = .* <([0-9]+\\.[0-9]+)>";
```

### 定时输出机制：
```c
gettimeofday(&current_time, NULL);
long time_diff = (current_time.tv_sec - last_output_time.tv_sec) * 1000000 +
                (current_time.tv_usec - last_output_time.tv_usec);
if (time_diff >= output_interval_us && syscall_count > 0) {
    print_syscall_stats();
}
```

### 统计数据结构：
```c
typedef struct {
    char name[64];          // 系统调用名称
    double total_time;      // 总时间（秒）
    int count;              // 调用次数
} syscall_stat_t;
```

## 可能遇到的问题

1. **正则表达式库**：确保系统支持POSIX正则表达式
2. **时间精度**：注意浮点数精度问题
3. **内存管理**：注意释放正则表达式资源
4. **输出缓冲**：确保使用fflush()

## 输出特征

成功的测试应该能看到：
- 每个解析成功的strace行都有"解析成功"提示
- 定时输出的统计信息（长时间运行的命令）
- 最终的系统调用统计，按百分比排序
- 每次统计后的80个\0字符分隔符
- 总执行时间和解析统计信息

## 下一步计划

完成第四步和第五步后，将继续实现：
- 第六步：系统调用统计和时间计算优化
- 第七步：输出格式化和定时输出完善
- 后续步骤...
