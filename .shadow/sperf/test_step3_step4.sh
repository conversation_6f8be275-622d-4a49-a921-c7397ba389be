#!/bin/bash

# 测试第三步和第四步功能的脚本

echo "=== 编译sperf程序 ==="
cd sperf
make clean
make

if [ $? -ne 0 ]; then
    echo "编译失败！"
    exit 1
fi

echo -e "\n=== 第三步测试：集成strace进行系统调用追踪 ==="

echo "测试1: 检查是否能找到strace"
which strace
if [ $? -ne 0 ]; then
    echo "警告: 系统中未找到strace，请安装strace"
    echo "安装命令: sudo apt-get install strace"
    exit 1
fi

echo -e "\n测试2: 使用strace追踪echo命令"
./sperf-64 echo "test"

echo -e "\n测试3: 使用strace追踪ls命令"
./sperf-64 ls

echo -e "\n=== 第四步测试：管道通信和strace输出捕获 ==="

echo "测试4: 捕获strace输出 - echo命令"
./sperf-64 echo "hello world"

echo -e "\n测试5: 捕获strace输出 - ls命令"
./sperf-64 ls /tmp

echo -e "\n测试6: 捕获strace输出 - 短时间运行的命令"
./sperf-64 date

echo -e "\n测试7: 捕获strace输出 - 稍长时间运行的命令"
./sperf-64 sleep 1

echo -e "\n=== 测试完成 ==="
