实验要求：实现系统调用 Profiler
实现一个命令行工具，它能启动另一个程序，并统计该程序中各个系统调用的占用时间。对于较短时间运行的程序，你可以一次性打印出耗时最多的几个系统调用。对于耗时较长的程序，你需要保证每秒大 打印 10 次 (不要过多) 系统调用的耗时信息。
总览
sperf _COMMAND_ [_ARG_]...
描述
它会运行 COMMAND 命令 (如果 COMMAND 是以 / 开头的绝对路径，则直接执行；否则在 PATH 环境变量中搜索到第一个存在且可执行的文件)，并为 COMMAND 传入 ARG 参数 (列表)，然后统计命令执行的系统调用所占的时间。例如执行 sperf find / 会在系统中执行 find /，并且在屏幕上显示出耗时最多的若干系统调用的时间。sperf 假设 COMMAND 是单进程、单线程的，无需处理多进程和多线程的情况。
⚠只能使用 execve
为了强迫大家理解 execve 系统调用，sperf 实现时，只能使用 execve 系统调用 ；使用 glibc 对 execve 的包装 (execl, execlp, execle, execv, execvp, execvpe) 将导致编译错误。
正确性标准
sperf 对 profiling 结果的输出格式没有特别要求，并假设被 trace 的程序是单进程程序 (Online Judge 测试用例会保证这一点)。如果进程没有发生系统调用，或系统调用耗时很长没有结束 (例如等待输入的 read 或大量数据的写入)，你相应的信息也可以不必更新 (即遇到很长的系统调用时，你不需要立即将这个系统调用的时间统计进来，可以等它执行结束后再统计)。
为了 Online Judge 判定方便，我们要求你的程序满足以下输出格式：
🗒实验要求：实现系统调用 Profiler
每次输出耗时 top 5 的系统调用、每个系统调用至多输出一次，包括系统调用的小写名字 (strace 输出的名字)和系统调用耗时比例按照 “(XX%)”。使用
printf("%s (%d%%)\n", syscall_name, ratio);
输出即可。在每次统计信息输出完毕后，打印 80 个 \0 (注意不是 '0'，是数值为 0 的字符，它们不会在终端上显示)。我们将以这 80 个\0 作为划分。

⚠及时清空 stdout 的缓冲区，请确保你在每一轮输出后使用 fflush

