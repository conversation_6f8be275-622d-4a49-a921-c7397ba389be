# sperf 第三步和第四步测试说明

## 前置条件
确保系统中安装了strace：
```bash
# Ubuntu/Debian
sudo apt-get install strace

# CentOS/RHEL
sudo yum install strace

# 检查是否安装
which strace
```

## 编译方法
```bash
cd sperf
make clean
make
```

## 第三步测试：集成strace进行系统调用追踪

### 测试用例1：追踪echo命令
```bash
./sperf-64 echo "test"
```
**期望输出：**
```
正在查找命令: echo
找到可执行文件: /bin/echo
找到strace: /usr/bin/strace
管道创建成功，读端: 3, 写端: 4
父进程开始读取strace输出(子进程PID: xxxx)...
子进程开始执行strace追踪: /bin/echo
=== strace输出 ===
execve("/bin/echo", ["echo", "test"], 0x7fff... /* 环境变量 */) = 0 <0.000123>
brk(NULL)                               = 0x... <0.000045>
...
write(1, "test\n", 5)                   = 5 <0.000067>
close(1)                                = 0 <0.000023>
...
子进程结束，最后读取剩余输出...
strace正常结束，退出码: 0
总共读取了 XX 行strace输出
```

### 测试用例2：追踪ls命令
```bash
./sperf-64 ls
```
**期望输出：**
```
正在查找命令: ls
找到可执行文件: /bin/ls
找到strace: /usr/bin/strace
管道创建成功，读端: 3, 写端: 4
父进程开始读取strace输出(子进程PID: xxxx)...
子进程开始执行strace追踪: /bin/ls
=== strace输出 ===
execve("/bin/ls", ["ls"], 0x7fff... /* 环境变量 */) = 0 <0.000156>
...
openat(AT_FDCWD, ".", O_RDONLY|O_NONBLOCK|O_CLOEXEC|O_DIRECTORY) = 3 <0.000089>
getdents64(3, /* 目录项 */, 32768)      = 1024 <0.000234>
...
子进程结束，最后读取剩余输出...
strace正常结束，退出码: 0
总共读取了 XX 行strace输出
```

## 第四步测试：管道通信和strace输出捕获

### 测试用例3：实时读取strace输出
```bash
./sperf-64 sleep 1
```
**期望输出：**
```
正在查找命令: sleep
找到可执行文件: /bin/sleep
找到strace: /usr/bin/strace
管道创建成功，读端: 3, 写端: 4
父进程开始读取strace输出(子进程PID: xxxx)...
子进程开始执行strace追踪: /bin/sleep
=== strace输出 ===
execve("/bin/sleep", ["sleep", "1"], 0x7fff... /* 环境变量 */) = 0 <0.000123>
...
nanosleep({tv_sec=1, tv_nsec=0}, 0x7fff...) = 0 <1.000567>
...
子进程结束，最后读取剩余输出...
strace正常结束，退出码: 0
总共读取了 XX 行strace输出
```

### 测试用例4：带参数的命令追踪
```bash
./sperf-64 echo "hello world"
```
**期望输出：**
```
正在查找命令: echo
找到可执行文件: /bin/echo
找到strace: /usr/bin/strace
管道创建成功，读端: 3, 写端: 4
父进程开始读取strace输出(子进程PID: xxxx)...
子进程开始执行strace追踪: /bin/echo
=== strace输出 ===
execve("/bin/echo", ["echo", "hello world"], 0x7fff... /* 环境变量 */) = 0 <0.000123>
...
write(1, "hello world\n", 12)           = 12 <0.000067>
...
子进程结束，最后读取剩余输出...
strace正常结束，退出码: 0
总共读取了 XX 行strace输出
```

## 验证要点

### 第三步验证要点：
1. **strace查找**：能正确找到系统中的strace程序
2. **strace集成**：能成功调用strace追踪目标程序
3. **参数构造**：正确构造strace的命令行参数（-T选项获取时间）
4. **execve调用**：使用execve执行strace而不是直接执行目标程序

### 第四步验证要点：
1. **管道创建**：成功创建管道用于进程间通信
2. **文件描述符重定向**：正确将strace的stderr重定向到管道
3. **非阻塞读取**：实现非阻塞读取避免程序阻塞
4. **实时输出**：能实时读取并显示strace的输出
5. **进程同步**：正确处理子进程的生命周期

## 关键技术实现

### strace参数构造：
```c
strace_argv[0] = "strace";
strace_argv[1] = "-T";  // 显示系统调用时间
strace_argv[2] = "-e";
strace_argv[3] = "trace=all";  // 追踪所有系统调用
```

### 管道和重定向：
```c
// 创建管道
pipe(pipefd);

// 子进程中重定向stderr到管道
dup2(pipefd[1], STDERR_FILENO);

// 父进程中设置非阻塞读取
fcntl(pipefd[0], F_SETFL, flags | O_NONBLOCK);
```

## 可能遇到的问题

1. **strace未安装**：需要安装strace程序
2. **权限问题**：某些系统可能需要特殊权限运行strace
3. **管道缓冲**：注意处理管道缓冲区满的情况
4. **进程同步**：确保正确处理子进程结束时机

## 输出特征

成功的测试应该能看到：
- strace的完整输出，包含系统调用名称和时间信息
- 每个系统调用后面的时间格式如 `<0.000123>`
- 能看到execve、brk、write等常见系统调用
- 程序能正常结束并显示统计信息

## 下一步计划

完成第三步和第四步后，将继续实现：
- 第五步：strace输出解析
- 第六步：系统调用统计和时间计算
- 后续步骤...
