#!/bin/bash

# 测试第四步和第五步功能的脚本

echo "=== 编译sperf程序（第四步和第五步） ==="
cd sperf
make clean
make

if [ $? -ne 0 ]; then
    echo "编译失败！"
    exit 1
fi

echo -e "\n=== 第四步测试：优化的管道通信 ==="

echo "测试1: 短时间命令 - echo"
./sperf-64 echo "hello world"

echo -e "\n测试2: 短时间命令 - date"
./sperf-64 date

echo -e "\n=== 第五步测试：strace输出解析 ==="

echo "测试3: 解析系统调用 - ls命令"
./sperf-64 ls /tmp

echo -e "\n测试4: 解析系统调用 - cat命令"
./sperf-64 cat /etc/hostname

echo -e "\n测试5: 长时间运行命令 - sleep（测试定时输出）"
./sperf-64 sleep 2

echo -e "\n测试6: 复杂命令 - find（测试实时统计）"
timeout 3 ./sperf-64 find /tmp -name "*.tmp" 2>/dev/null || echo "find命令超时结束"

echo -e "\n=== 测试完成 ==="
