手动测试指令（请在sperf目录下执行）：

1. 编译程序：
   make clean && make

2. 测试PATH搜索功能：
   ./sperf-64 ls
   ./sperf-64 echo
   ./sperf-64 date

3. 测试绝对路径：
   ./sperf-64 /bin/ls
   ./sperf-64 /bin/echo "test"

4. 测试错误情况：
   ./sperf-64 nonexistent_command
   ./sperf-64

5. 测试参数传递：
   ./sperf-64 echo "hello world"
   ./sperf-64 ls /tmp
   ./sperf-64 echo "参数1" "参数2"

6. 测试其他命令：
   ./sperf-64 whoami
   ./sperf-64 pwd
   ./sperf-64 cat /etc/hostname

预期结果：
- 所有有效命令都应该正常执行
- 能看到"正在查找命令"和"找到可执行文件"的输出
- 能看到"父进程等待子进程"和"子进程正常结束"的输出
- 目标程序的输出应该正常显示
- 错误情况应该有适当的错误信息
