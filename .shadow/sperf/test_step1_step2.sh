#!/bin/bash

# 测试第一步和第二步功能的脚本

echo "=== 编译sperf程序 ==="
cd sperf
make clean
make

if [ $? -ne 0 ]; then
    echo "编译失败！"
    exit 1
fi

echo -e "\n=== 第一步测试：PATH搜索功能 ==="

echo "测试1: 查找ls命令（应该找到/bin/ls或/usr/bin/ls）"
./sperf-64 ls

echo -e "\n测试2: 使用绝对路径/bin/ls"
./sperf-64 /bin/ls

echo -e "\n测试3: 查找不存在的命令"
./sperf-64 nonexistent_command

echo -e "\n测试4: 无参数调用（应该显示使用说明）"
./sperf-64

echo -e "\n=== 第二步测试：基本进程创建和execve调用 ==="

echo "测试5: 执行echo命令"
./sperf-64 echo "hello world"

echo -e "\n测试6: 执行ls命令列出/tmp目录"
./sperf-64 ls /tmp

echo -e "\n测试7: 执行带多个参数的命令"
./sperf-64 echo "参数1" "参数2" "参数3"

echo -e "\n测试8: 执行date命令"
./sperf-64 date

echo -e "\n=== 测试完成 ==="
