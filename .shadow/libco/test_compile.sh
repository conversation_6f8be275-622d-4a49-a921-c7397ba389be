#!/bin/bash

# 测试编译脚本
echo "开始测试协程库编译..."

# 清理之前的编译文件
rm -f *.so *.o DECS/test_step12_64 DECS/test_step12_32

echo "编译64位版本..."
# 编译64位共享库
gcc -fPIC -shared -m64 -O1 -std=gnu11 -ggdb -Wall -Werror -Wno-unused-result -Wno-unused-value -Wno-unused-variable -U_FORTIFY_SOURCE co.c -o libco-64.so

if [ $? -eq 0 ]; then
    echo "64位共享库编译成功"
    
    # 编译64位测试程序
    gcc -I. -L. -m64 DECS/test_step12.c -o DECS/test_step12_64 -lco-64
    
    if [ $? -eq 0 ]; then
        echo "64位测试程序编译成功"
    else
        echo "64位测试程序编译失败"
    fi
else
    echo "64位共享库编译失败"
fi

echo "编译32位版本..."
# 编译32位共享库
gcc -fPIC -shared -m32 -O1 -std=gnu11 -ggdb -Wall -Werror -Wno-unused-result -Wno-unused-value -Wno-unused-variable -U_FORTIFY_SOURCE co.c -o libco-32.so

if [ $? -eq 0 ]; then
    echo "32位共享库编译成功"
    
    # 编译32位测试程序
    gcc -I. -L. -m32 DECS/test_step12.c -o DECS/test_step12_32 -lco-32
    
    if [ $? -eq 0 ]; then
        echo "32位测试程序编译成功"
    else
        echo "32位测试程序编译失败"
    fi
else
    echo "32位共享库编译失败"
fi

echo "编译测试完成"

# 如果编译成功，运行测试
if [ -f DECS/test_step12_64 ]; then
    echo ""
    echo "运行64位测试..."
    LD_LIBRARY_PATH=. ./DECS/test_step12_64
fi

if [ -f DECS/test_step12_32 ]; then
    echo ""
    echo "运行32位测试..."
    LD_LIBRARY_PATH=. ./DECS/test_step12_32
fi
