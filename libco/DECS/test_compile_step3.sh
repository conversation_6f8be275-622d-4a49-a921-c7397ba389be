#!/bin/bash

# 第三步编译测试脚本
echo "=== 第三步协程调度器编译测试 ==="

# 进入libco目录
cd ..

# 清理之前的编译文件
echo "清理编译文件..."
rm -f *.so *.o DECS/simple_test_64 DECS/simple_test_32

echo "编译协程库..."
# 编译64位共享库
gcc -fPIC -shared -m64 -O1 -std=gnu11 -ggdb -Wall -Werror -Wno-unused-result -Wno-unused-value -Wno-unused-variable -U_FORTIFY_SOURCE co.c -o libco-64.so

if [ $? -eq 0 ]; then
    echo "✓ 64位共享库编译成功"
    
    # 编译64位简单测试程序
    gcc -I. -L. -m64 DECS/simple_test.c -o DECS/simple_test_64 -lco-64
    
    if [ $? -eq 0 ]; then
        echo "✓ 64位测试程序编译成功"
    else
        echo "✗ 64位测试程序编译失败"
        exit 1
    fi
else
    echo "✗ 64位共享库编译失败"
    exit 1
fi

echo "编译32位版本..."
# 编译32位共享库
gcc -fPIC -shared -m32 -O1 -std=gnu11 -ggdb -Wall -Werror -Wno-unused-result -Wno-unused-value -Wno-unused-variable -U_FORTIFY_SOURCE co.c -o libco-32.so

if [ $? -eq 0 ]; then
    echo "✓ 32位共享库编译成功"
    
    # 编译32位测试程序
    gcc -I. -L. -m32 DECS/simple_test.c -o DECS/simple_test_32 -lco-32
    
    if [ $? -eq 0 ]; then
        echo "✓ 32位测试程序编译成功"
    else
        echo "✗ 32位测试程序编译失败"
        exit 1
    fi
else
    echo "✗ 32位共享库编译失败"
    exit 1
fi

echo ""
echo "=== 运行简单测试 ==="

# 运行64位测试
if [ -f DECS/simple_test_64 ]; then
    echo "运行64位测试..."
    echo "----------------------------------------"
    LD_LIBRARY_PATH=. ./DECS/simple_test_64
    if [ $? -eq 0 ]; then
        echo "✓ 64位测试运行成功"
    else
        echo "✗ 64位测试运行失败"
    fi
    echo "----------------------------------------"
    echo ""
fi

# 运行32位测试
if [ -f DECS/simple_test_32 ]; then
    echo "运行32位测试..."
    echo "----------------------------------------"
    LD_LIBRARY_PATH=. ./DECS/simple_test_32
    if [ $? -eq 0 ]; then
        echo "✓ 32位测试运行成功"
    else
        echo "✗ 32位测试运行失败"
    fi
    echo "----------------------------------------"
    echo ""
fi

echo "=== 运行官方测试 ==="
# 运行官方测试用例
cd tests
make clean
make test
test_result=$?
cd ..

if [ $test_result -eq 0 ]; then
    echo "✓ 官方测试通过"
else
    echo "✗ 官方测试失败"
fi

echo ""
echo "=== 第三步测试完成 ==="
echo "如果所有测试都通过，说明第三步实现基本正确"
