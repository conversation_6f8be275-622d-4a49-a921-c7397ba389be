/*
 * 测试第一步和第二步实现的简单测试程序
 * 用于验证协程数据结构和上下文切换机制
 */

#include <stdio.h>
#include "co.h"

// 测试1：基本协程创建和切换
void test_basic_switch() {
    printf("=== 测试1：基本协程创建和切换 ===\n");
    
    // 简单的协程函数
    void test_func(void *arg) {
        const char *name = (const char *)arg;
        for (int i = 0; i < 3; i++) {
            printf("%s-%d ", name, i);
            co_yield();
        }
    }
    
    struct co *co1 = co_start("A", test_func, "A");
    struct co *co2 = co_start("B", test_func, "B");
    
    if (co1 == NULL || co2 == NULL) {
        printf("错误：协程创建失败\n");
        return;
    }
    
    printf("协程创建成功，开始切换测试...\n");
    
    // 手动调用几次co_yield来观察切换
    for (int i = 0; i < 10; i++) {
        co_yield();
    }
    
    printf("\n等待协程结束...\n");
    co_wait(co1);
    co_wait(co2);
    printf("测试1完成\n\n");
}

// 测试2：协程状态管理
void test_status_management() {
    printf("=== 测试2：协程状态管理 ===\n");
    
    void status_func(void *arg) {
        printf("协程 %s 开始执行\n", (const char *)arg);
        co_yield();
        printf("协程 %s 继续执行\n", (const char *)arg);
        co_yield();
        printf("协程 %s 即将结束\n", (const char *)arg);
    }
    
    printf("创建协程...\n");
    struct co *co1 = co_start("test", status_func, "test");
    
    if (co1 == NULL) {
        printf("错误：协程创建失败\n");
        return;
    }
    
    printf("第一次yield...\n");
    co_yield();
    
    printf("第二次yield...\n");
    co_yield();
    
    printf("第三次yield...\n");
    co_yield();
    
    printf("等待协程结束...\n");
    co_wait(co1);
    
    printf("测试2完成\n\n");
}

// 测试3：多协程内存管理
void test_memory_management() {
    printf("=== 测试3：多协程内存管理 ===\n");
    
    void memory_func(void *arg) {
        int id = *(int *)arg;
        printf("协程 %d 运行 ", id);
        co_yield();
        printf("协程 %d 结束 ", id);
    }
    
    // 创建多个协程测试内存管理
    for (int round = 0; round < 2; round++) {
        printf("--- 第 %d 轮测试 ---\n", round + 1);
        
        int ids[3] = {1, 2, 3};
        struct co *cos[3];
        
        // 创建协程
        for (int i = 0; i < 3; i++) {
            cos[i] = co_start("test", memory_func, &ids[i]);
            if (cos[i] == NULL) {
                printf("错误：协程 %d 创建失败\n", i);
                return;
            }
        }
        
        // 让协程运行
        for (int i = 0; i < 8; i++) {
            co_yield();
        }
        
        printf("\n");
        
        // 等待所有协程结束
        for (int i = 0; i < 3; i++) {
            co_wait(cos[i]);
        }
        
        printf("第 %d 轮完成\n", round + 1);
    }
    
    printf("测试3完成\n\n");
}

// 测试4：协程栈空间测试
void test_stack_space() {
    printf("=== 测试4：协程栈空间测试 ===\n");
    
    void stack_func(void *arg) {
        // 在栈上分配一些空间测试栈是否正常工作
        char buffer[1024];
        int id = *(int *)arg;
        
        // 初始化buffer
        for (int i = 0; i < 1024; i++) {
            buffer[i] = (char)(id + i % 256);
        }
        
        printf("协程 %d 栈测试开始 ", id);
        co_yield();
        
        // 验证buffer内容是否正确
        int correct = 1;
        for (int i = 0; i < 1024; i++) {
            if (buffer[i] != (char)(id + i % 256)) {
                correct = 0;
                break;
            }
        }
        
        printf("协程 %d 栈测试%s ", id, correct ? "成功" : "失败");
    }
    
    int ids[2] = {10, 20};
    struct co *cos[2];
    
    for (int i = 0; i < 2; i++) {
        cos[i] = co_start("stack_test", stack_func, &ids[i]);
        if (cos[i] == NULL) {
            printf("错误：协程 %d 创建失败\n", i);
            return;
        }
    }
    
    // 让协程运行
    for (int i = 0; i < 6; i++) {
        co_yield();
    }
    
    printf("\n");
    
    for (int i = 0; i < 2; i++) {
        co_wait(cos[i]);
    }
    
    printf("测试4完成\n\n");
}

int main() {
    printf("开始协程库前两步实现测试\n");
    printf("测试平台：");
#if defined(__x86_64__)
    printf("x86-64\n");
#elif defined(__i386__)
    printf("x86-32\n");
#else
    printf("未知架构\n");
#endif
    printf("\n");
    
    test_basic_switch();
    test_status_management();
    test_memory_management();
    test_stack_space();
    
    printf("所有测试完成！\n");
    printf("如果看到以上输出且没有段错误，说明前两步实现基本正确。\n");
    
    return 0;
}
