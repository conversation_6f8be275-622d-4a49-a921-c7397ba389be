#!/bin/bash

# 第三步完整测试套件
echo "========================================"
echo "    第三步协程调度器完整测试套件"
echo "========================================"

# 进入libco目录
cd ..

# 设置颜色输出
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

# 测试结果统计
total_tests=0
passed_tests=0

# 辅助函数：运行测试并记录结果
run_test() {
    local test_name="$1"
    local test_command="$2"
    
    echo ""
    echo "----------------------------------------"
    echo "运行测试: $test_name"
    echo "----------------------------------------"
    
    total_tests=$((total_tests + 1))
    
    if eval "$test_command"; then
        echo -e "${GREEN}✓ $test_name 通过${NC}"
        passed_tests=$((passed_tests + 1))
        return 0
    else
        echo -e "${RED}✗ $test_name 失败${NC}"
        return 1
    fi
}

# 清理函数
cleanup() {
    echo "清理编译文件..."
    rm -f *.so *.o DECS/simple_test_64 DECS/simple_test_32
    rm -f DECS/test_step3_64 DECS/test_step3_32
    rm -f DECS/comprehensive_64 DECS/comprehensive_32
}

# 开始测试
echo "开始第三步协程调度器测试..."
echo "测试时间: $(date)"
echo ""

# 清理之前的编译文件
cleanup

# 测试1：编译测试
echo "========================================"
echo "阶段1: 编译测试"
echo "========================================"

run_test "64位协程库编译" "gcc -fPIC -shared -m64 -O1 -std=gnu11 -ggdb -Wall -Werror -Wno-unused-result -Wno-unused-value -Wno-unused-variable -U_FORTIFY_SOURCE co.c -o libco-64.so"

run_test "32位协程库编译" "gcc -fPIC -shared -m32 -O1 -std=gnu11 -ggdb -Wall -Werror -Wno-unused-result -Wno-unused-value -Wno-unused-variable -U_FORTIFY_SOURCE co.c -o libco-32.so"

# 测试2：简单功能测试
echo ""
echo "========================================"
echo "阶段2: 简单功能测试"
echo "========================================"

run_test "64位简单测试编译" "gcc -I. -L. -m64 DECS/simple_test.c -o DECS/simple_test_64 -lco-64"
run_test "64位简单测试运行" "LD_LIBRARY_PATH=. ./DECS/simple_test_64"

run_test "32位简单测试编译" "gcc -I. -L. -m32 DECS/simple_test.c -o DECS/simple_test_32 -lco-32"
run_test "32位简单测试运行" "LD_LIBRARY_PATH=. ./DECS/simple_test_32"

# 测试3：调度器功能测试
echo ""
echo "========================================"
echo "阶段3: 调度器功能测试"
echo "========================================"

run_test "64位调度器测试编译" "gcc -I. -L. -m64 DECS/test_step3_scheduler.c -o DECS/test_step3_64 -lco-64"
run_test "64位调度器测试运行" "LD_LIBRARY_PATH=. ./DECS/test_step3_64"

run_test "32位调度器测试编译" "gcc -I. -L. -m32 DECS/test_step3_scheduler.c -o DECS/test_step3_32 -lco-32"
run_test "32位调度器测试运行" "LD_LIBRARY_PATH=. ./DECS/test_step3_32"

# 测试4：综合测试
echo ""
echo "========================================"
echo "阶段4: 综合测试"
echo "========================================"

run_test "64位综合测试编译" "gcc -I. -L. -m64 DECS/test_step3_comprehensive.c -o DECS/comprehensive_64 -lco-64"
run_test "64位综合测试运行" "LD_LIBRARY_PATH=. ./DECS/comprehensive_64"

run_test "32位综合测试编译" "gcc -I. -L. -m32 DECS/test_step3_comprehensive.c -o DECS/comprehensive_32 -lco-32"
run_test "32位综合测试运行" "LD_LIBRARY_PATH=. ./DECS/comprehensive_32"

# 测试5：官方测试用例
echo ""
echo "========================================"
echo "阶段5: 官方测试用例"
echo "========================================"

run_test "官方测试用例" "cd tests && make clean && make test && cd .."

# 测试结果汇总
echo ""
echo "========================================"
echo "测试结果汇总"
echo "========================================"

echo "总测试数: $total_tests"
echo "通过测试: $passed_tests"
echo "失败测试: $((total_tests - passed_tests))"

if [ $passed_tests -eq $total_tests ]; then
    echo -e "${GREEN}🎉 所有测试通过！第三步实现正确！${NC}"
    echo ""
    echo "第三步改进总结："
    echo "✓ 多级优先级调度器"
    echo "✓ 高效就绪队列管理"
    echo "✓ 改进的随机数生成器"
    echo "✓ 统一的状态管理"
    echo "✓ 时间片统计功能"
    echo "✓ 跨平台兼容性"
    exit 0
else
    echo -e "${RED}❌ 部分测试失败，请检查实现${NC}"
    echo ""
    echo "调试建议："
    echo "1. 检查编译错误和警告"
    echo "2. 验证协程状态转换逻辑"
    echo "3. 确认就绪队列操作正确性"
    echo "4. 检查内存分配和释放"
    echo "5. 验证上下文切换实现"
    exit 1
fi
