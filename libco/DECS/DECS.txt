实验要求：使用一个操作系统线程实现主动切换的多个执行流
在这个实验中，我们实现轻量级的用户态协程 (coroutine，“协同程序”)，也称为 green threads、user-level threads，可以在一个不支持线程的操作系统上实现共享内存多任务并发。即我们希望实现 C 语言的 “函数”，它能够：
被 co_start() 调用，从头开始运行；在运行到中途时，调用 co_yield() 被 “切换” 出去；
稍后有其他协程调用 co_yield() 后，选择一个先前被切换的协程继续执行。

2.1 Coroutine APIs
实现协程库 co.h 中定义的 API：
struct co *co_start(const char *name, void (*func)(void *), void *arg);
void       co_yield();
void       co_wait(struct co *co);
协程库的使用和线程库非常类似：
co_start(name, func, arg) 创建一个新的协程，并返回一个指向 struct co 的指针 (类似于 pthread_create)。新创建的协程从函数 func 开始执行，并传入参数 arg。新创建的协程不会立即执行，而是调用 co_start 的协程继续执行。
使用协程的应用程序不需要知道 struct co 的具体定义，因此请把这个定义留在 co.c 中；框架代码中并没有限定 struct co 结构体的设计，所以你可以自由发挥。
co_start 返回的 struct co 指针需要分配内存。我们推荐使用 malloc() 分配。
co_wait(co) 表示当前协程需要等待，直到 co 协程的执行完成才能继续执行 (类似于 pthread_join)。在被等待的协程结束后、 co_wait() 返回前，co_start 分配的 struct co 需要被释放。如果你使用 malloc()，使用 free() 释放即可。
因此，每个协程只能被 co_wait 一次 (使用协程库的程序应当保证除了初始协程外，其他协程都必须被 co_wait 恰好一次，否则会造成内存泄漏)。
co_yield() 实现协程的切换。协程运行后一直在 CPU 上执行，直到 func 函数返回或调用 co_yield 使当前运行的协程暂时放弃执行。co_yield 时若系统中有多个可运行的协程时 (包括当前协程)，你应当随机选择下一个系统中可运行的协程。
main 函数的执行也是一个协程，因此可以在 main 中调用 co_yield 或 co_wait。main 函数返回后，无论有多少协程，进程都将直接终止。


2.2 使用协程库
下面是协程库使用的一个例子，创建两个 (永不结束的) 协程，分别打印 a 和 b。由于 co_yield() 之后切换到的协程是随机的 (可能切换到它自己)，因此你将会看到随机的 ab 交替出现的序列，例如 ababbabaaaabbaa...
#include <stdio.h>
#include "co.h"

void entry(void *arg) {
    while (1) {
        printf("%s", (const char *)arg);
        co_yield();
    }
}

int main() {
    struct co *co1 = co_start("co1", entry, "a");
    struct co *co2 = co_start("co2", entry, "b");
    co_wait(co1); // never returns
    co_wait(co2);
}
当然，协程有可能会返回，例如在下面的例子 (测试程序) 中，两个协程会交替执行，共享 counter 变量：
#include <stdio.h>
#include "co.h"

int count = 1; // 协程之间共享

void entry(void *arg) {
    for (int i = 0; i < 5; i++) {
        printf("%s[%d] ", (const char *)arg, count++);
        co_yield();
    }
}

int main() {
    struct co *co1 = co_start("co1", entry, "a");
    struct co *co2 = co_start("co2", entry, "b");
    co_wait(co1);
    co_wait(co2);
    printf("Done\n");
}

正确的协程实现应该输出类似于以下的结果：字母是随机的 (a 或 b)，数字则从 1 到 10 递增。
b[1] a[2] b[3] b[4] a[5] b[6] b[7] a[8] a[9] a[10] Done
3. 正确性标准
🗒实验要求：将所有代码实现在 co.c 中
不要修改 co.h。Online Judge 在评测时仅拷贝你的 co.c 文件。struct co 可以直接在 .c 中定义 (头文件不需要给出具体的结构体定义)。好消息是，我们已经为大家提供了基础测试用例，测试用例有两组：
(Easy) 创建两个协程，每个协程会循环 100 次，然后打印当前协程的名字和全局计数器 g_count 的数值，然后执行 g_count++。
(Hard) 创建两个生产者、两个消费者。每个生产者每次会向队列中插入一个数据，然后执行 co_yield() 让其他 (随机的) 协程执行；每个消费者会检查队列是否为空，如果非空会从队列中取出头部的元素。无论队列是否为空，之后都会调用 co_yield() 让其他 (随机的) 协程执行。
执行 make test 会在 x86-64 和 x86-32 两个环境下运行你的代码——如果你看到第一个测试用例打印出数字 X/Y-0 到 X/Y-199、第二个测试用例打印出 libco-200 到 libco-399，说明你的实现基本正确；否则请调试你的代码。
Online Judge 上会运行类似的测试 (也会在 x86-64 和 x86-32 两个平台上运行)，但规模可能稍大一些。你可以假设：
每个协程的堆栈使用不超过 64 KiB；
任意时刻系统中的协程数量不会超过 128 个 (包括 main 对应的协程)。协程 wait 返回后协程的资源应当被回收——我们可能会创建大量的协程执行-等待-销毁、执行-等待-销毁。因此如果你的资源没有及时回收，可能会发生 Memory Limit Exceeded 问题。
还需要注意的是，提交的代码不要有任何多余的输出，否则可能会被 Online Judge 判错。
如果你希望在本地运行时保留调试信息并且不想在提交到 Online Judge 时费力地删除散落在程序中的调试信息，你可以尝试用一个你本地的宏来控制输出的行为，例如
#ifdef LOCAL_MACHINE
    #define debug(...) printf(__VA_ARGS__)
#else
    #define debug()
#endif
然后通过增加 -DLOCAL_MACHINE 的编译选项来实现输出控制——在 Online Judge 上，所有的调试输出都会消失。

4. 第一步和第二步实现说明

4.1 第一步：协程数据结构设计
已完成的协程数据结构包含以下关键组件：

struct co {
    const char *name;           // 协程名称
    void (*func)(void *);       // 协程入口函数
    void *arg;                  // 传递给协程函数的参数
    enum co_status status;      // 协程当前状态（NEW/RUNNING/WAITING/DEAD）
    char *stack;                // 协程栈指针（指向栈底）
    void *sp;                   // 保存的栈指针
    struct co *waiter;          // 等待当前协程结束的协程
};

关键设计决策：
- 每个协程分配64KB独立栈空间
- 使用枚举类型管理协程状态
- 支持协程间的等待关系
- 全局协程列表支持最多128个协程

4.2 第二步：上下文切换机制
实现了跨平台的汇编级上下文切换：

- 支持x86-64和x86-32两种架构
- 保存/恢复所有通用寄存器
- 正确处理栈指针切换
- 使用内联汇编确保性能

上下文切换流程：
1. 保存当前协程的所有寄存器到栈中
2. 保存当前栈指针到协程结构体
3. 切换到目标协程的栈指针
4. 从栈中恢复目标协程的寄存器
5. 继续执行目标协程

4.3 前两步完成后的测试方法

测试1：基本协程创建和切换
创建一个简单的测试程序验证协程创建和基本切换功能：

```c
#include <stdio.h>
#include "co.h"

void test_func(void *arg) {
    const char *name = (const char *)arg;
    for (int i = 0; i < 3; i++) {
        printf("%s-%d ", name, i);
        co_yield();
    }
}

int main() {
    struct co *co1 = co_start("A", test_func, "A");
    struct co *co2 = co_start("B", test_func, "B");

    // 手动调用几次co_yield来观察切换
    for (int i = 0; i < 10; i++) {
        co_yield();
    }

    co_wait(co1);
    co_wait(co2);
    printf("Done\n");
    return 0;
}
```

预期输出：应该看到A和B的交替输出，如"A-0 B-0 A-1 B-1 A-2 B-2 Done"

测试2：协程状态管理
验证协程状态正确转换：

```c
#include <stdio.h>
#include "co.h"

void simple_func(void *arg) {
    printf("协程 %s 开始执行\n", (const char *)arg);
    co_yield();
    printf("协程 %s 继续执行\n", (const char *)arg);
    co_yield();
    printf("协程 %s 即将结束\n", (const char *)arg);
}

int main() {
    printf("创建协程...\n");
    struct co *co1 = co_start("test", simple_func, "test");

    printf("第一次yield...\n");
    co_yield();

    printf("第二次yield...\n");
    co_yield();

    printf("第三次yield...\n");
    co_yield();

    printf("等待协程结束...\n");
    co_wait(co1);

    printf("测试完成\n");
    return 0;
}
```

测试3：内存管理验证
确保协程资源正确分配和释放：

```c
#include <stdio.h>
#include "co.h"

void memory_test_func(void *arg) {
    int id = *(int *)arg;
    printf("协程 %d 运行\n", id);
    co_yield();
    printf("协程 %d 结束\n", id);
}

int main() {
    // 创建多个协程测试内存管理
    for (int round = 0; round < 3; round++) {
        printf("=== 第 %d 轮测试 ===\n", round + 1);

        int ids[5] = {1, 2, 3, 4, 5};
        struct co *cos[5];

        // 创建协程
        for (int i = 0; i < 5; i++) {
            cos[i] = co_start("test", memory_test_func, &ids[i]);
        }

        // 让协程运行
        for (int i = 0; i < 10; i++) {
            co_yield();
        }

        // 等待所有协程结束
        for (int i = 0; i < 5; i++) {
            co_wait(cos[i]);
        }
    }

    printf("内存管理测试完成\n");
    return 0;
}
```

4.4 编译和运行测试
使用以下命令编译和测试：

```bash
# 编译协程库
cd libco
make

# 编译测试程序（以test1.c为例）
gcc -I. -L. -m64 test1.c -o test1-64 -lco-64
gcc -I. -L. -m32 test1.c -o test1-32 -lco-32

# 运行测试
LD_LIBRARY_PATH=. ./test1-64
LD_LIBRARY_PATH=. ./test1-32
```

4.5 预期结果
如果前两步实现正确，应该观察到：
1. 协程能够正确创建和初始化
2. 上下文切换工作正常，协程间能够交替执行
3. 协程状态正确管理
4. 内存正确分配和释放，无内存泄漏
5. 在x86-64和x86-32平台都能正常工作

如果出现段错误或异常行为，需要检查：
- 栈指针设置是否正确
- 寄存器保存/恢复是否完整
- 内存分配是否成功
- 协程状态转换是否正确

