/*
 * 简单的第三步功能验证测试
 * 用于快速验证第三步实现是否基本正确
 */

#include <stdio.h>
#include "../co.h"

void simple_func(void *arg) {
    const char *name = (const char *)arg;
    printf("%s 开始\n", name);
    co_yield();
    printf("%s 中间\n", name);
    co_yield();
    printf("%s 结束\n", name);
}

int main() {
    printf("简单的第三步测试开始\n");
    
    // 创建两个协程
    struct co *co1 = co_start("协程1", simple_func, "协程1");
    struct co *co2 = co_start("协程2", simple_func, "协程2");
    
    if (!co1 || !co2) {
        printf("协程创建失败\n");
        return 1;
    }
    
    printf("协程创建成功，开始调度\n");
    
    // 运行协程
    for (int i = 0; i < 8; i++) {
        printf("主协程 yield %d\n", i + 1);
        co_yield();
    }
    
    printf("等待协程结束\n");
    co_wait(co1);
    co_wait(co2);
    
    printf("测试完成\n");
    return 0;
}
