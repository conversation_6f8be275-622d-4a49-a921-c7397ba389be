#!/bin/bash

# 官方测试用例运行脚本
echo "=== 运行官方测试用例 ==="

# 进入libco目录
cd ..

echo "编译协程库..."
# 清理并重新编译
make clean
make

if [ $? -ne 0 ]; then
    echo "✗ 协程库编译失败"
    exit 1
fi

echo "✓ 协程库编译成功"

echo ""
echo "进入tests目录运行官方测试..."
cd tests

# 清理并编译测试程序
make clean
make all

if [ $? -ne 0 ]; then
    echo "✗ 测试程序编译失败"
    exit 1
fi

echo "✓ 测试程序编译成功"

echo ""
echo "=== 运行官方测试 ==="
echo "预期结果："
echo "- 第一个测试：应该看到 X0 到 X199 和 Y0 到 Y199 的输出"
echo "- 第二个测试：应该看到 libco-200 到 libco-399 的输出"
echo ""

# 运行测试
make test

test_result=$?

echo ""
if [ $test_result -eq 0 ]; then
    echo "✓ 官方测试通过！"
    echo "如果看到了预期的输出格式，说明协程库实现正确"
else
    echo "✗ 官方测试失败"
    echo "请检查协程库的实现"
fi

echo ""
echo "=== 测试分析 ==="
echo "检查要点："
echo "1. 数字序列是否连续（0-199 和 200-399）"
echo "2. X和Y的输出是否交替出现（随机性）"
echo "3. 是否有段错误或异常终止"
echo "4. 内存是否正确释放（无内存泄漏）"

cd ..
echo "测试完成"
