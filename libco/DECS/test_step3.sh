#!/bin/bash

# 第三步测试脚本
echo "=== 第三步协程调度器测试 ==="

# 进入libco目录
cd ..

# 清理之前的编译文件
echo "清理编译文件..."
rm -f *.so *.o DECS/test_step3_64 DECS/test_step3_32

echo "编译协程库..."
# 编译64位共享库
gcc -fPIC -shared -m64 -O1 -std=gnu11 -ggdb -Wall -Werror -Wno-unused-result -Wno-unused-value -Wno-unused-variable -U_FORTIFY_SOURCE co.c -o libco-64.so

if [ $? -eq 0 ]; then
    echo "✓ 64位共享库编译成功"
    
    # 编译64位测试程序
    gcc -I. -L. -m64 DECS/test_step3_scheduler.c -o DECS/test_step3_64 -lco-64
    
    if [ $? -eq 0 ]; then
        echo "✓ 64位测试程序编译成功"
    else
        echo "✗ 64位测试程序编译失败"
    fi
else
    echo "✗ 64位共享库编译失败"
fi

echo "编译32位版本..."
# 编译32位共享库
gcc -fPIC -shared -m32 -O1 -std=gnu11 -ggdb -Wall -Werror -Wno-unused-result -Wno-unused-value -Wno-unused-variable -U_FORTIFY_SOURCE co.c -o libco-32.so

if [ $? -eq 0 ]; then
    echo "✓ 32位共享库编译成功"
    
    # 编译32位测试程序
    gcc -I. -L. -m32 DECS/test_step3_scheduler.c -o DECS/test_step3_32 -lco-32
    
    if [ $? -eq 0 ]; then
        echo "✓ 32位测试程序编译成功"
    else
        echo "✗ 32位测试程序编译失败"
    fi
else
    echo "✗ 32位共享库编译失败"
fi

echo ""
echo "=== 运行测试 ==="

# 运行64位测试
if [ -f DECS/test_step3_64 ]; then
    echo "运行64位测试..."
    echo "----------------------------------------"
    LD_LIBRARY_PATH=. ./DECS/test_step3_64
    echo "----------------------------------------"
    echo ""
fi

# 运行32位测试
if [ -f DECS/test_step3_32 ]; then
    echo "运行32位测试..."
    echo "----------------------------------------"
    LD_LIBRARY_PATH=. ./DECS/test_step3_32
    echo "----------------------------------------"
    echo ""
fi

echo "=== 运行官方测试 ==="
# 运行官方测试用例
cd tests
make clean
make test
cd ..

echo ""
echo "=== 测试完成 ==="
echo "请检查以上输出，确认："
echo "1. 编译无错误和警告"
echo "2. 第三步测试正常运行"
echo "3. 官方测试用例通过"
echo "4. 没有段错误或内存错误"
