/*
 * 第三步综合测试：验证调度器改进的所有功能
 * 包括就绪队列管理、状态转换、内存管理等
 */

#include <stdio.h>
#include <stdlib.h>
#include <string.h>
#include "../co.h"

// 全局计数器，用于验证执行顺序
static int execution_count = 0;

// 测试1：协程创建和基本调度
void test_basic_creation() {
    printf("=== 测试1：协程创建和基本调度 ===\n");
    
    void simple_worker(void *arg) {
        int id = *(int *)arg;
        printf("协程%d开始执行 ", id);
        co_yield();
        printf("协程%d继续执行 ", id);
        co_yield();
        printf("协程%d结束 ", id);
    }
    
    int ids[3] = {1, 2, 3};
    struct co *workers[3];
    
    // 创建协程
    for (int i = 0; i < 3; i++) {
        workers[i] = co_start("worker", simple_worker, &ids[i]);
        if (!workers[i]) {
            printf("错误：协程%d创建失败\n", i + 1);
            return;
        }
    }
    
    printf("创建了3个协程，开始调度...\n");
    
    // 运行协程
    for (int i = 0; i < 12; i++) {
        co_yield();
    }
    
    printf("\n等待所有协程结束...\n");
    for (int i = 0; i < 3; i++) {
        co_wait(workers[i]);
    }
    
    printf("测试1完成\n\n");
}

// 测试2：协程状态管理
void test_state_management() {
    printf("=== 测试2：协程状态管理 ===\n");
    
    static struct co *child_co = NULL;
    
    void child_func(void *arg) {
        printf("子协程开始 ");
        co_yield();
        printf("子协程中间 ");
        co_yield();
        printf("子协程结束 ");
    }
    
    void parent_func(void *arg) {
        printf("父协程开始 ");
        child_co = co_start("child", child_func, NULL);
        if (child_co) {
            printf("父协程等待子协程 ");
            co_wait(child_co);
            printf("父协程：子协程已结束 ");
        }
        printf("父协程结束 ");
    }
    
    struct co *parent_co = co_start("parent", parent_func, NULL);
    if (!parent_co) {
        printf("错误：父协程创建失败\n");
        return;
    }
    
    printf("测试协程等待关系...\n");
    
    // 让协程运行
    for (int i = 0; i < 8; i++) {
        co_yield();
    }
    
    printf("\n等待父协程结束...\n");
    co_wait(parent_co);
    
    printf("测试2完成\n\n");
}

// 测试3：大量协程压力测试
void test_stress() {
    printf("=== 测试3：大量协程压力测试 ===\n");
    
    void stress_worker(void *arg) {
        int id = *(int *)arg;
        execution_count++;
        printf("%d ", id);
        co_yield();
        execution_count++;
    }
    
    const int num_cos = 20;
    int ids[20];
    struct co *cos[20];
    
    execution_count = 0;
    
    // 创建大量协程
    for (int i = 0; i < num_cos; i++) {
        ids[i] = i + 1;
        cos[i] = co_start("stress", stress_worker, &ids[i]);
        if (!cos[i]) {
            printf("错误：协程%d创建失败\n", i + 1);
            return;
        }
    }
    
    printf("创建了%d个协程，开始压力测试...\n", num_cos);
    
    // 让所有协程运行
    for (int i = 0; i < num_cos * 3; i++) {
        co_yield();
    }
    
    printf("\n等待所有协程结束...\n");
    for (int i = 0; i < num_cos; i++) {
        co_wait(cos[i]);
    }
    
    printf("执行计数: %d (预期: %d)\n", execution_count, num_cos * 2);
    printf("测试3完成\n\n");
}

// 测试4：随机调度验证
void test_random_scheduling() {
    printf("=== 测试4：随机调度验证 ===\n");
    
    void random_worker(void *arg) {
        char name = *(char *)arg;
        for (int i = 0; i < 5; i++) {
            printf("%c", name);
            co_yield();
        }
    }
    
    char names[4] = {'A', 'B', 'C', 'D'};
    struct co *workers[4];
    
    // 创建4个协程
    for (int i = 0; i < 4; i++) {
        workers[i] = co_start("random", random_worker, &names[i]);
        if (!workers[i]) {
            printf("错误：协程%d创建失败\n", i + 1);
            return;
        }
    }
    
    printf("创建了4个协程，观察随机调度...\n输出序列: ");
    
    // 运行协程观察随机性
    for (int i = 0; i < 25; i++) {
        co_yield();
    }
    
    printf("\n等待所有协程结束...\n");
    for (int i = 0; i < 4; i++) {
        co_wait(workers[i]);
    }
    
    printf("测试4完成\n\n");
}

// 测试5：内存管理验证
void test_memory_management() {
    printf("=== 测试5：内存管理验证 ===\n");
    
    void memory_worker(void *arg) {
        int round = *(int *)arg;
        // 在栈上分配一些内存测试栈管理
        char buffer[1024];
        for (int i = 0; i < 1024; i++) {
            buffer[i] = (char)(round + i % 256);
        }
        
        printf("轮次%d ", round);
        co_yield();
        
        // 验证栈内容是否正确
        int correct = 1;
        for (int i = 0; i < 1024; i++) {
            if (buffer[i] != (char)(round + i % 256)) {
                correct = 0;
                break;
            }
        }
        
        printf("栈验证%s ", correct ? "OK" : "FAIL");
    }
    
    // 多轮创建和销毁协程
    for (int round = 1; round <= 3; round++) {
        printf("第%d轮内存测试: ", round);
        
        struct co *cos[5];
        int round_val = round;
        
        // 创建协程
        for (int i = 0; i < 5; i++) {
            cos[i] = co_start("memory", memory_worker, &round_val);
            if (!cos[i]) {
                printf("错误：协程创建失败\n");
                return;
            }
        }
        
        // 运行协程
        for (int i = 0; i < 12; i++) {
            co_yield();
        }
        
        // 等待协程结束
        for (int i = 0; i < 5; i++) {
            co_wait(cos[i]);
        }
        
        printf("\n");
    }
    
    printf("测试5完成\n\n");
}

int main() {
    printf("第三步综合测试开始\n");
    printf("测试平台: ");
#if defined(__x86_64__)
    printf("x86-64\n");
#elif defined(__i386__)
    printf("x86-32\n");
#else
    printf("未知架构\n");
#endif
    printf("\n");
    
    test_basic_creation();
    test_state_management();
    test_stress();
    test_random_scheduling();
    test_memory_management();
    
    printf("=== 第三步综合测试完成 ===\n");
    printf("如果所有测试都正常运行且没有段错误，说明第三步实现正确。\n");
    printf("关键验证点：\n");
    printf("1. 协程创建和调度正常\n");
    printf("2. 协程状态转换正确\n");
    printf("3. 大量协程处理稳定\n");
    printf("4. 随机调度有效\n");
    printf("5. 内存管理无泄漏\n");
    
    return 0;
}
