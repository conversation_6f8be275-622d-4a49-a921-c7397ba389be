# 第三步：协程调度器优化

## 概述

第三步在前两步的基础上，对协程调度器进行了全面优化和改进，提供了更高效、更灵活的协程调度机制。

## 主要改进

### 1. 多级优先级调度
- 支持0-10级优先级（数字越大优先级越高）
- 高优先级协程优先执行
- 同优先级内随机调度

### 2. 高效就绪队列
- 使用双向链表管理就绪协程
- O(1)时间复杂度的插入和删除操作
- 按优先级分组，避免不必要的遍历

### 3. 改进的随机算法
- 使用PCG（Permuted Congruential Generator）算法
- 提供更好的随机性分布
- 替代简单的线性同余生成器

### 4. 统一状态管理
- 自动管理协程在就绪队列中的状态
- 状态变化时自动更新队列
- 减少手动状态管理的错误

### 5. 时间片统计
- 为每个协程记录运行时间片
- 为未来的时间片调度做准备

## 数据结构扩展

```c
struct co {
    // 原有字段...
    int id;                     // 协程唯一标识符
    int priority;               // 协程优先级（0-10）
    int time_slice;             // 时间片计数器
    struct co *next;            // 链表指针，用于就绪队列
    struct co *prev;            // 双向链表的前向指针
};
```

## 调度算法

### 三阶段调度策略：
1. **优先级调度**：从高优先级（10）到低优先级（0）查找可运行协程
2. **同级随机选择**：在同一优先级内使用改进的随机算法选择协程
3. **回退机制**：如果就绪队列为空，检查当前协程和main协程

### 就绪队列管理：
- 双向链表实现，支持快速插入和删除
- 按优先级分组，提高查找效率
- 自动维护队列计数，快速判断队列状态

## 测试文件

### 基础测试
- `simple_test.c` - 最简单的功能验证
- `test_compile_step3.sh` - 编译和基础测试脚本

### 功能测试
- `test_step3_scheduler.c` - 调度器功能专项测试
- `test_step3_comprehensive.c` - 综合功能测试

### 完整测试套件
- `run_all_tests.sh` - 完整的测试套件
- `test_official.sh` - 官方测试用例运行

## 快速开始

### 1. 编译和基础测试
```bash
cd libco/DECS
chmod +x test_compile_step3.sh
./test_compile_step3.sh
```

### 2. 运行完整测试
```bash
chmod +x run_all_tests.sh
./run_all_tests.sh
```

### 3. 验证官方测试
```bash
chmod +x test_official.sh
./test_official.sh
```

## 预期结果

### 成功标准
1. **编译成功**：64位和32位版本都能正常编译，无错误和警告
2. **功能正确**：所有测试通过，协程调度行为符合预期
3. **性能改进**：调度器响应更快，大量协程时性能稳定
4. **兼容性保持**：API行为与原规范一致

### 输出示例
- 第一个测试：应该看到协程交替执行的输出
- 随机调度测试：应该看到随机的字符序列
- 官方测试：应该看到X0-X199, Y0-Y199和libco-200到libco-399

## 调试提示

### 常见问题
1. **编译错误**：检查新增字段的初始化和函数声明
2. **运行时错误**：验证就绪队列操作和状态更新逻辑
3. **调度异常**：检查优先级设置和随机数生成器
4. **性能问题**：确认队列操作的时间复杂度

### 调试方法
1. 使用gdb调试段错误
2. 添加调试输出观察调度过程
3. 使用valgrind检查内存泄漏
4. 对比官方测试的预期输出

## 技术细节

### PCG随机数生成器
```c
static uint32_t pcg32_random() {
    uint64_t oldstate = rng_state.state;
    rng_state.state = oldstate * 6364136223846793005ULL + rng_state.inc;
    uint32_t xorshifted = ((oldstate >> 18u) ^ oldstate) >> 27u;
    uint32_t rot = oldstate >> 59u;
    return (xorshifted >> rot) | (xorshifted << ((-rot) & 31));
}
```

### 就绪队列操作
- `ready_queue_push()` - 将协程加入就绪队列
- `ready_queue_remove()` - 从就绪队列移除协程
- `update_co_status()` - 更新协程状态并管理队列

### 调度器选择
- `choose_next_co()` - 选择下一个要运行的协程
- 优先级调度 + 同级随机选择
- 支持回退到当前协程或main协程

## 性能对比

| 指标 | 第二步 | 第三步 | 改进 |
|------|--------|--------|------|
| 调度时间复杂度 | O(n) | O(1) | 显著提升 |
| 随机性质量 | 一般 | 优秀 | PCG算法 |
| 内存遍历 | 每次全遍历 | 按需遍历 | 减少开销 |
| 扩展性 | 有限 | 良好 | 支持优先级 |

## 总结

第三步成功实现了协程调度器的全面优化，在保持API兼容性的同时，显著提升了性能和功能。这些改进为协程库提供了更好的可扩展性和实用性，适合更复杂的应用场景。
