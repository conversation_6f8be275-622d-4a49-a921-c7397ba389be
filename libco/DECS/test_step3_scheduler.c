/*
 * 第三步测试：改进的协程调度器测试
 * 测试优先级调度、就绪队列管理和改进的随机选择算法
 */

#include <stdio.h>
#include <stdlib.h>
#include <string.h>
#include "../co.h"

// 测试1：优先级调度测试
void test_priority_scheduling() {
    printf("=== 测试1：优先级调度测试 ===\n");
    
    // 高优先级协程函数
    void high_priority_func(void *arg) {
        const char *name = (const char *)arg;
        for (int i = 0; i < 3; i++) {
            printf("[高优先级] %s-%d ", name, i);
            co_yield();
        }
    }
    
    // 低优先级协程函数
    void low_priority_func(void *arg) {
        const char *name = (const char *)arg;
        for (int i = 0; i < 3; i++) {
            printf("[低优先级] %s-%d ", name, i);
            co_yield();
        }
    }
    
    // 创建不同优先级的协程
    struct co *high_co = co_start("HIGH", high_priority_func, "HIGH");
    struct co *low_co = co_start("LOW", low_priority_func, "LOW");
    
    if (!high_co || !low_co) {
        printf("错误：协程创建失败\n");
        return;
    }
    
    printf("创建了高优先级和低优先级协程，开始调度测试...\n");
    
    // 运行协程
    for (int i = 0; i < 12; i++) {
        co_yield();
    }
    
    printf("\n等待协程结束...\n");
    co_wait(high_co);
    co_wait(low_co);
    printf("优先级调度测试完成\n\n");
}

// 测试2：多协程随机调度测试
void test_random_scheduling() {
    printf("=== 测试2：多协程随机调度测试 ===\n");
    
    void worker_func(void *arg) {
        int id = *(int *)arg;
        for (int i = 0; i < 5; i++) {
            printf("协程%d-第%d次运行 ", id, i + 1);
            co_yield();
        }
    }
    
    // 创建多个协程
    int ids[4] = {1, 2, 3, 4};
    struct co *workers[4];
    
    for (int i = 0; i < 4; i++) {
        workers[i] = co_start("worker", worker_func, &ids[i]);
        if (!workers[i]) {
            printf("错误：协程 %d 创建失败\n", i + 1);
            return;
        }
    }
    
    printf("创建了4个协程，测试随机调度...\n");
    
    // 让协程运行
    for (int i = 0; i < 25; i++) {
        co_yield();
    }
    
    printf("\n等待所有协程结束...\n");
    for (int i = 0; i < 4; i++) {
        co_wait(workers[i]);
    }
    printf("随机调度测试完成\n\n");
}

// 测试3：协程状态管理测试
void test_status_management() {
    printf("=== 测试3：协程状态管理测试 ===\n");
    
    void status_test_func(void *arg) {
        const char *name = (const char *)arg;
        printf("%s 开始执行\n", name);
        co_yield();
        printf("%s 继续执行\n", name);
        co_yield();
        printf("%s 即将结束\n", name);
    }
    
    struct co *test_co = co_start("STATUS_TEST", status_test_func, "STATUS_TEST");
    if (!test_co) {
        printf("错误：协程创建失败\n");
        return;
    }
    
    printf("协程创建成功，测试状态转换...\n");
    
    for (int i = 0; i < 6; i++) {
        printf("主协程第%d次yield\n", i + 1);
        co_yield();
    }
    
    printf("等待协程结束...\n");
    co_wait(test_co);
    printf("状态管理测试完成\n\n");
}

// 测试4：就绪队列压力测试
void test_ready_queue_stress() {
    printf("=== 测试4：就绪队列压力测试 ===\n");
    
    void stress_func(void *arg) {
        int id = *(int *)arg;
        printf("协程%d运行 ", id);
        co_yield();
        printf("协程%d结束 ", id);
    }
    
    // 创建大量协程测试就绪队列管理
    const int num_coroutines = 10;
    int ids[10];
    struct co *coroutines[10];
    
    for (int i = 0; i < num_coroutines; i++) {
        ids[i] = i + 1;
        coroutines[i] = co_start("stress", stress_func, &ids[i]);
        if (!coroutines[i]) {
            printf("错误：协程 %d 创建失败\n", i + 1);
            return;
        }
    }
    
    printf("创建了%d个协程，测试就绪队列管理...\n", num_coroutines);
    
    // 让所有协程运行
    for (int i = 0; i < num_coroutines * 3; i++) {
        co_yield();
    }
    
    printf("\n等待所有协程结束...\n");
    for (int i = 0; i < num_coroutines; i++) {
        co_wait(coroutines[i]);
    }
    printf("就绪队列压力测试完成\n\n");
}

// 测试5：协程等待链测试
void test_wait_chain() {
    printf("=== 测试5：协程等待链测试 ===\n");
    
    static struct co *child_co = NULL;
    
    void child_func(void *arg) {
        printf("子协程开始执行\n");
        for (int i = 0; i < 3; i++) {
            printf("子协程工作中 %d\n", i + 1);
            co_yield();
        }
        printf("子协程结束\n");
    }
    
    void parent_func(void *arg) {
        printf("父协程开始执行\n");
        child_co = co_start("child", child_func, NULL);
        if (child_co) {
            printf("父协程等待子协程...\n");
            co_wait(child_co);
            printf("父协程：子协程已结束\n");
        }
        printf("父协程结束\n");
    }
    
    struct co *parent_co = co_start("parent", parent_func, NULL);
    if (!parent_co) {
        printf("错误：父协程创建失败\n");
        return;
    }
    
    printf("创建父协程，测试等待链...\n");
    
    // 让协程运行
    for (int i = 0; i < 10; i++) {
        co_yield();
    }
    
    printf("等待父协程结束...\n");
    co_wait(parent_co);
    printf("等待链测试完成\n\n");
}

int main() {
    printf("开始第三步协程调度器测试\n");
    printf("测试平台：");
#if defined(__x86_64__)
    printf("x86-64\n");
#elif defined(__i386__)
    printf("x86-32\n");
#else
    printf("未知架构\n");
#endif
    printf("\n");
    
    test_priority_scheduling();
    test_random_scheduling();
    test_status_management();
    test_ready_queue_stress();
    test_wait_chain();
    
    printf("第三步调度器测试完成！\n");
    printf("如果看到以上输出且没有段错误，说明第三步实现基本正确。\n");
    printf("注意观察：\n");
    printf("1. 高优先级协程是否优先执行\n");
    printf("2. 同优先级协程是否随机调度\n");
    printf("3. 协程状态转换是否正确\n");
    printf("4. 大量协程创建和销毁是否正常\n");
    printf("5. 协程等待关系是否正确处理\n");
    
    return 0;
}
