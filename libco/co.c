#include "co.h"
#include <stdlib.h>
#include <stdint.h>
#include <string.h>
#include <assert.h>
#include <stdio.h>

// 协程状态枚举
enum co_status {
    CO_NEW,      // 新创建，尚未运行
    CO_RUNNING,  // 正在运行
    CO_WAITING,  // 等待其他协程
    CO_DEAD      // 已结束
};

// 协程结构体定义
struct co {
    const char *name;           // 协程名称
    void (*func)(void *);       // 协程入口函数
    void *arg;                  // 传递给协程函数的参数
    enum co_status status;      // 协程当前状态
    char *stack;                // 协程栈指针（指向栈底）
    void *sp;                   // 保存的栈指针
    struct co *waiter;          // 等待当前协程结束的协程

    // 第三步新增：调度器优化相关字段
    int id;                     // 协程唯一标识符
    int priority;               // 协程优先级（0-10，数字越大优先级越高）
    int time_slice;             // 时间片计数器
    struct co *next;            // 链表指针，用于就绪队列
    struct co *prev;            // 双向链表的前向指针
};

// 第三步：改进的调度器数据结构
typedef struct {
    struct co *head;            // 就绪队列头指针
    struct co *tail;            // 就绪队列尾指针
    int count;                  // 就绪队列中的协程数量
} ready_queue_t;

// 全局变量
static struct co *current = NULL;      // 当前正在运行的协程
static struct co *co_list[128];        // 协程列表，最多128个协程
static int co_count = 0;               // 当前协程数量
static int next_co_id = 1;             // 下一个协程ID

// 第三步新增：多级就绪队列（按优先级分组）
static ready_queue_t ready_queues[11]; // 0-10优先级的就绪队列
static int total_ready_count = 0;       // 所有就绪队列中的协程总数

// 第三步新增：改进的随机数生成器
static struct {
    uint32_t state;
    uint32_t inc;
} rng_state = {1, 1};

// 主协程（main函数对应的协程）
static struct co main_co = {
    .name = "main",
    .func = NULL,
    .arg = NULL,
    .status = CO_RUNNING,
    .stack = NULL,
    .sp = NULL,
    .waiter = NULL,
    .id = 0,                    // main协程ID为0
    .priority = 5,              // 默认优先级
    .time_slice = 0,
    .next = NULL,
    .prev = NULL
};

// 协程栈大小（64KB）
#define STACK_SIZE (1 << 16)

// 函数前向声明
static void co_switch(void **old_sp, void *new_sp);
static void update_co_status(struct co *co, enum co_status new_status);

// 协程包装函数，用于处理协程结束后的清理工作
static void co_wrapper() {
    // 执行协程函数
    current->func(current->arg);

    // 协程函数执行完毕，标记为已结束
    update_co_status(current, CO_DEAD);

    // 如果有协程在等待当前协程，唤醒它
    if (current->waiter) {
        update_co_status(current->waiter, CO_RUNNING);
    }

    // 切换到其他协程
    co_yield();

    // 这里不应该被执行到
    assert(0);
}

// 内联汇编实现的上下文切换函数
// 支持x86-64和x86-32两种架构
static void co_switch(void **old_sp, void *new_sp) {
#if defined(__x86_64__)
    // x86-64架构的上下文切换
    asm volatile (
        "pushq %%rax\n\t"       // 保存rax寄存器
        "pushq %%rbx\n\t"       // 保存rbx寄存器
        "pushq %%rcx\n\t"       // 保存rcx寄存器
        "pushq %%rdx\n\t"       // 保存rdx寄存器
        "pushq %%rsi\n\t"       // 保存rsi寄存器
        "pushq %%rdi\n\t"       // 保存rdi寄存器
        "pushq %%rbp\n\t"       // 保存rbp寄存器
        "pushq %%r8\n\t"        // 保存r8寄存器
        "pushq %%r9\n\t"        // 保存r9寄存器
        "pushq %%r10\n\t"       // 保存r10寄存器
        "pushq %%r11\n\t"       // 保存r11寄存器
        "pushq %%r12\n\t"       // 保存r12寄存器
        "pushq %%r13\n\t"       // 保存r13寄存器
        "pushq %%r14\n\t"       // 保存r14寄存器
        "pushq %%r15\n\t"       // 保存r15寄存器
        "movq %%rsp, %0\n\t"    // 保存当前栈指针到old_sp
        "movq %1, %%rsp\n\t"    // 切换到新的栈指针
        "popq %%r15\n\t"        // 恢复r15寄存器
        "popq %%r14\n\t"        // 恢复r14寄存器
        "popq %%r13\n\t"        // 恢复r13寄存器
        "popq %%r12\n\t"        // 恢复r12寄存器
        "popq %%r11\n\t"        // 恢复r11寄存器
        "popq %%r10\n\t"        // 恢复r10寄存器
        "popq %%r9\n\t"         // 恢复r9寄存器
        "popq %%r8\n\t"         // 恢复r8寄存器
        "popq %%rbp\n\t"        // 恢复rbp寄存器
        "popq %%rdi\n\t"        // 恢复rdi寄存器
        "popq %%rsi\n\t"        // 恢复rsi寄存器
        "popq %%rdx\n\t"        // 恢复rdx寄存器
        "popq %%rcx\n\t"        // 恢复rcx寄存器
        "popq %%rbx\n\t"        // 恢复rbx寄存器
        "popq %%rax\n\t"        // 恢复rax寄存器
        : "=m" (*old_sp)        // 输出：保存当前栈指针
        : "m" (new_sp)          // 输入：新的栈指针
        : "memory"              // 内存屏障
    );
#elif defined(__i386__)
    // x86-32架构的上下文切换
    asm volatile (
        "pushl %%eax\n\t"       // 保存eax寄存器
        "pushl %%ebx\n\t"       // 保存ebx寄存器
        "pushl %%ecx\n\t"       // 保存ecx寄存器
        "pushl %%edx\n\t"       // 保存edx寄存器
        "pushl %%esi\n\t"       // 保存esi寄存器
        "pushl %%edi\n\t"       // 保存edi寄存器
        "pushl %%ebp\n\t"       // 保存ebp寄存器
        "movl %%esp, %0\n\t"    // 保存当前栈指针到old_sp
        "movl %1, %%esp\n\t"    // 切换到新的栈指针
        "popl %%ebp\n\t"        // 恢复ebp寄存器
        "popl %%edi\n\t"        // 恢复edi寄存器
        "popl %%esi\n\t"        // 恢复esi寄存器
        "popl %%edx\n\t"        // 恢复edx寄存器
        "popl %%ecx\n\t"        // 恢复ecx寄存器
        "popl %%ebx\n\t"        // 恢复ebx寄存器
        "popl %%eax\n\t"        // 恢复eax寄存器
        : "=m" (*old_sp)        // 输出：保存当前栈指针
        : "m" (new_sp)          // 输入：新的栈指针
        : "memory"              // 内存屏障
    );
#else
    #error "Unsupported architecture"
#endif
}

// 第三步：改进的随机数生成器（PCG算法）
static uint32_t pcg32_random() {
    uint64_t oldstate = rng_state.state;
    rng_state.state = oldstate * 6364136223846793005ULL + rng_state.inc;
    uint32_t xorshifted = ((oldstate >> 18u) ^ oldstate) >> 27u;
    uint32_t rot = oldstate >> 59u;
    return (xorshifted >> rot) | (xorshifted << ((-rot) & 31));
}

// 第三步：就绪队列管理函数
static void ready_queue_init() {
    // 初始化所有优先级的就绪队列
    for (int i = 0; i < 11; i++) {
        ready_queues[i].head = NULL;
        ready_queues[i].tail = NULL;
        ready_queues[i].count = 0;
    }
    total_ready_count = 0;
}

// 第三步：将协程加入就绪队列
static void ready_queue_push(struct co *co) {
    if (!co || co->priority < 0 || co->priority > 10) {
        return; // 无效的协程或优先级
    }

    ready_queue_t *queue = &ready_queues[co->priority];

    // 设置链表指针
    co->next = NULL;
    co->prev = queue->tail;

    if (queue->tail) {
        queue->tail->next = co;
    } else {
        queue->head = co; // 队列为空，设置头指针
    }

    queue->tail = co;
    queue->count++;
    total_ready_count++;
}

// 第三步：从就绪队列中移除协程
static void ready_queue_remove(struct co *co) {
    if (!co || co->priority < 0 || co->priority > 10) {
        return;
    }

    ready_queue_t *queue = &ready_queues[co->priority];

    // 更新链表指针
    if (co->prev) {
        co->prev->next = co->next;
    } else {
        queue->head = co->next; // 移除的是头节点
    }

    if (co->next) {
        co->next->prev = co->prev;
    } else {
        queue->tail = co->prev; // 移除的是尾节点
    }

    co->next = co->prev = NULL;
    queue->count--;
    total_ready_count--;
}

// 初始化协程系统（在第一次调用co_start时自动调用）
static void co_init() {
    // 将main协程设置为当前协程
    current = &main_co;

    // 清空协程列表
    for (int i = 0; i < 128; i++) {
        co_list[i] = NULL;
    }
    co_count = 0;

    // 第三步：初始化就绪队列
    ready_queue_init();

    // 初始化随机数生成器
    rng_state.state = 1;
    rng_state.inc = 1;
}

// 查找一个空闲的协程槽位
static int find_free_slot() {
    for (int i = 0; i < 128; i++) {
        if (co_list[i] == NULL) {
            return i;
        }
    }
    return -1; // 没有空闲槽位
}

// 第三步：改进的协程调度算法
static struct co *choose_next_co() {
    // 如果没有就绪的协程，检查当前协程是否可以继续运行
    if (total_ready_count == 0) {
        if (current && (current->status == CO_RUNNING || current->status == CO_NEW)) {
            return current;
        }
        return NULL;
    }

    // 第一阶段：优先级调度
    // 从高优先级到低优先级查找可运行的协程
    for (int priority = 10; priority >= 0; priority--) {
        ready_queue_t *queue = &ready_queues[priority];
        if (queue->count == 0) {
            continue;
        }

        // 在同一优先级内使用改进的随机选择
        if (queue->count == 1) {
            return queue->head;
        }

        // 随机选择同优先级中的一个协程
        uint32_t choice = pcg32_random() % queue->count;
        struct co *selected = queue->head;
        for (uint32_t i = 0; i < choice && selected; i++) {
            selected = selected->next;
        }

        if (selected) {
            return selected;
        }
    }

    // 第二阶段：如果就绪队列中没有找到合适的协程，检查当前协程
    if (current && (current->status == CO_RUNNING || current->status == CO_NEW)) {
        return current;
    }

    // 第三阶段：检查main协程
    if (current != &main_co && main_co.status == CO_RUNNING) {
        return &main_co;
    }

    return NULL;
}

// 第三步：更新协程状态并管理就绪队列
static void update_co_status(struct co *co, enum co_status new_status) {
    if (!co) return;

    enum co_status old_status = co->status;
    co->status = new_status;

    // 根据状态变化更新就绪队列
    if (old_status == CO_RUNNING || old_status == CO_NEW) {
        if (new_status != CO_RUNNING && new_status != CO_NEW) {
            // 从就绪状态变为非就绪状态，从就绪队列中移除
            ready_queue_remove(co);
        }
    } else {
        if (new_status == CO_RUNNING || new_status == CO_NEW) {
            // 从非就绪状态变为就绪状态，加入就绪队列
            ready_queue_push(co);
        }
    }
}

// 实现co_start函数：创建新协程
struct co *co_start(const char *name, void (*func)(void *), void *arg) {
    // 第一次调用时初始化协程系统
    static int initialized = 0;
    if (!initialized) {
        co_init();
        initialized = 1;
    }

    // 查找空闲槽位
    int slot = find_free_slot();
    if (slot == -1) {
        // 没有空闲槽位，返回NULL
        return NULL;
    }

    // 分配协程结构体
    struct co *co = (struct co *)malloc(sizeof(struct co));
    if (!co) {
        return NULL;
    }

    // 分配协程栈空间
    co->stack = (char *)malloc(STACK_SIZE);
    if (!co->stack) {
        free(co);
        return NULL;
    }

    // 初始化协程结构体
    co->name = name;
    co->func = func;
    co->arg = arg;
    co->status = CO_NEW;
    co->waiter = NULL;

    // 第三步：初始化新增字段
    co->id = next_co_id++;
    co->priority = 5;           // 默认优先级为5（中等）
    co->time_slice = 0;
    co->next = NULL;
    co->prev = NULL;

    // 设置协程栈指针（栈从高地址向低地址增长）
    // 在栈顶预留空间并设置返回地址为co_wrapper
    char *stack_top = co->stack + STACK_SIZE;

#if defined(__x86_64__)
    // x86-64架构：8字节对齐，预留返回地址空间
    stack_top -= sizeof(void *);
    *(void **)stack_top = (void *)co_wrapper;

    // 为寄存器保存预留空间（15个寄存器 * 8字节）
    stack_top -= 15 * sizeof(void *);
    co->sp = stack_top;
#elif defined(__i386__)
    // x86-32架构：4字节对齐，预留返回地址空间
    stack_top -= sizeof(void *);
    *(void **)stack_top = (void *)co_wrapper;

    // 为寄存器保存预留空间（7个寄存器 * 4字节）
    stack_top -= 7 * sizeof(void *);
    co->sp = stack_top;
#endif

    // 将协程加入列表
    co_list[slot] = co;
    co_count++;

    // 第三步：将新协程加入就绪队列
    ready_queue_push(co);

    return co;
}

// 实现co_yield函数：协程主动让出CPU
void co_yield() {
    // 如果协程系统未初始化，先初始化
    if (current == NULL) {
        co_init();
        return;
    }

    // 选择下一个要运行的协程
    struct co *next = choose_next_co();

    // 如果没有其他可运行的协程，继续运行当前协程
    if (next == NULL || next == current) {
        return;
    }

    // 保存当前协程信息
    struct co *prev = current;
    current = next;

    // 第三步：使用新的状态更新函数
    if (next->status == CO_NEW) {
        update_co_status(next, CO_RUNNING);
    }

    // 更新时间片计数器
    if (prev) {
        prev->time_slice++;
    }
    if (next) {
        next->time_slice = 0; // 重置新协程的时间片
    }

    // 执行上下文切换
    co_switch(&prev->sp, next->sp);
}

// 实现co_wait函数：等待指定协程结束
void co_wait(struct co *co) {
    // 如果协程系统未初始化，先初始化
    if (current == NULL) {
        co_init();
    }

    // 如果协程已经结束，直接释放资源并返回
    if (co->status == CO_DEAD) {
        // 从协程列表中移除
        for (int i = 0; i < 128; i++) {
            if (co_list[i] == co) {
                co_list[i] = NULL;
                co_count--;
                break;
            }
        }

        // 释放协程资源
        free(co->stack);
        free(co);
        return;
    }

    // 设置当前协程为等待者
    co->waiter = current;
    update_co_status(current, CO_WAITING);

    // 持续让出CPU直到目标协程结束
    while (co->status != CO_DEAD) {
        co_yield();
    }

    // 协程已结束，从列表中移除并释放资源
    for (int i = 0; i < 128; i++) {
        if (co_list[i] == co) {
            co_list[i] = NULL;
            co_count--;
            break;
        }
    }

    // 第三步：确保从就绪队列中移除
    ready_queue_remove(co);

    // 释放协程资源
    free(co->stack);
    free(co);

    // 恢复当前协程状态
    update_co_status(current, CO_RUNNING);
}
