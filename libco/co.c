#include "co.h"
#include <stdlib.h>
#include <stdint.h>
#include <string.h>
#include <assert.h>
#include <stdio.h>

// 协程状态枚举
enum co_status {
    CO_NEW,      // 新创建，尚未运行
    CO_RUNNING,  // 正在运行
    CO_WAITING,  // 等待其他协程
    CO_DEAD      // 已结束
};

// 协程结构体定义
struct co {
    const char *name;           // 协程名称
    void (*func)(void *);       // 协程入口函数
    void *arg;                  // 传递给协程函数的参数
    enum co_status status;      // 协程当前状态
    char *stack;                // 协程栈指针（指向栈底）
    void *sp;                   // 保存的栈指针
    struct co *waiter;          // 等待当前协程结束的协程
};

// 全局变量
static struct co *current = NULL;      // 当前正在运行的协程
static struct co *co_list[128];        // 协程列表，最多128个协程
static int co_count = 0;               // 当前协程数量

// 主协程（main函数对应的协程）
static struct co main_co = {
    .name = "main",
    .func = NULL,
    .arg = NULL,
    .status = CO_RUNNING,
    .stack = NULL,
    .sp = NULL,
    .waiter = NULL
};

// 协程栈大小（64KB）
#define STACK_SIZE (1 << 16)

// 上下文切换函数声明（汇编实现）
static void co_switch(void **old_sp, void *new_sp);

// 协程包装函数，用于处理协程结束后的清理工作
static void co_wrapper() {
    // 执行协程函数
    current->func(current->arg);

    // 协程函数执行完毕，标记为已结束
    current->status = CO_DEAD;

    // 如果有协程在等待当前协程，唤醒它
    if (current->waiter) {
        current->waiter->status = CO_RUNNING;
    }

    // 切换到其他协程
    co_yield();

    // 这里不应该被执行到
    assert(0);
}

// 内联汇编实现的上下文切换函数
// 支持x86-64和x86-32两种架构
static void co_switch(void **old_sp, void *new_sp) {
#if defined(__x86_64__)
    // x86-64架构的上下文切换
    asm volatile (
        "pushq %%rax\n\t"       // 保存rax寄存器
        "pushq %%rbx\n\t"       // 保存rbx寄存器
        "pushq %%rcx\n\t"       // 保存rcx寄存器
        "pushq %%rdx\n\t"       // 保存rdx寄存器
        "pushq %%rsi\n\t"       // 保存rsi寄存器
        "pushq %%rdi\n\t"       // 保存rdi寄存器
        "pushq %%rbp\n\t"       // 保存rbp寄存器
        "pushq %%r8\n\t"        // 保存r8寄存器
        "pushq %%r9\n\t"        // 保存r9寄存器
        "pushq %%r10\n\t"       // 保存r10寄存器
        "pushq %%r11\n\t"       // 保存r11寄存器
        "pushq %%r12\n\t"       // 保存r12寄存器
        "pushq %%r13\n\t"       // 保存r13寄存器
        "pushq %%r14\n\t"       // 保存r14寄存器
        "pushq %%r15\n\t"       // 保存r15寄存器
        "movq %%rsp, %0\n\t"    // 保存当前栈指针到old_sp
        "movq %1, %%rsp\n\t"    // 切换到新的栈指针
        "popq %%r15\n\t"        // 恢复r15寄存器
        "popq %%r14\n\t"        // 恢复r14寄存器
        "popq %%r13\n\t"        // 恢复r13寄存器
        "popq %%r12\n\t"        // 恢复r12寄存器
        "popq %%r11\n\t"        // 恢复r11寄存器
        "popq %%r10\n\t"        // 恢复r10寄存器
        "popq %%r9\n\t"         // 恢复r9寄存器
        "popq %%r8\n\t"         // 恢复r8寄存器
        "popq %%rbp\n\t"        // 恢复rbp寄存器
        "popq %%rdi\n\t"        // 恢复rdi寄存器
        "popq %%rsi\n\t"        // 恢复rsi寄存器
        "popq %%rdx\n\t"        // 恢复rdx寄存器
        "popq %%rcx\n\t"        // 恢复rcx寄存器
        "popq %%rbx\n\t"        // 恢复rbx寄存器
        "popq %%rax\n\t"        // 恢复rax寄存器
        : "=m" (*old_sp)        // 输出：保存当前栈指针
        : "m" (new_sp)          // 输入：新的栈指针
        : "memory"              // 内存屏障
    );
#elif defined(__i386__)
    // x86-32架构的上下文切换
    asm volatile (
        "pushl %%eax\n\t"       // 保存eax寄存器
        "pushl %%ebx\n\t"       // 保存ebx寄存器
        "pushl %%ecx\n\t"       // 保存ecx寄存器
        "pushl %%edx\n\t"       // 保存edx寄存器
        "pushl %%esi\n\t"       // 保存esi寄存器
        "pushl %%edi\n\t"       // 保存edi寄存器
        "pushl %%ebp\n\t"       // 保存ebp寄存器
        "movl %%esp, %0\n\t"    // 保存当前栈指针到old_sp
        "movl %1, %%esp\n\t"    // 切换到新的栈指针
        "popl %%ebp\n\t"        // 恢复ebp寄存器
        "popl %%edi\n\t"        // 恢复edi寄存器
        "popl %%esi\n\t"        // 恢复esi寄存器
        "popl %%edx\n\t"        // 恢复edx寄存器
        "popl %%ecx\n\t"        // 恢复ecx寄存器
        "popl %%ebx\n\t"        // 恢复ebx寄存器
        "popl %%eax\n\t"        // 恢复eax寄存器
        : "=m" (*old_sp)        // 输出：保存当前栈指针
        : "m" (new_sp)          // 输入：新的栈指针
        : "memory"              // 内存屏障
    );
#else
    #error "Unsupported architecture"
#endif
}

// 初始化协程系统（在第一次调用co_start时自动调用）
static void co_init() {
    // 将main协程设置为当前协程
    current = &main_co;

    // 清空协程列表
    for (int i = 0; i < 128; i++) {
        co_list[i] = NULL;
    }
    co_count = 0;
}

// 查找一个空闲的协程槽位
static int find_free_slot() {
    for (int i = 0; i < 128; i++) {
        if (co_list[i] == NULL) {
            return i;
        }
    }
    return -1; // 没有空闲槽位
}

// 随机选择下一个可运行的协程
static struct co *choose_next_co() {
    // 收集所有可运行的协程（包括当前协程）
    struct co *runnable[129]; // 最多128个协程 + main协程
    int runnable_count = 0;

    // 检查main协程是否可运行
    if (current != &main_co && main_co.status == CO_RUNNING) {
        runnable[runnable_count++] = &main_co;
    }

    // 检查其他协程是否可运行
    for (int i = 0; i < 128; i++) {
        if (co_list[i] != NULL &&
            (co_list[i]->status == CO_NEW || co_list[i]->status == CO_RUNNING)) {
            runnable[runnable_count++] = co_list[i];
        }
    }

    // 如果当前协程也可运行，加入列表
    if (current->status == CO_RUNNING || current->status == CO_NEW) {
        runnable[runnable_count++] = current;
    }

    // 如果没有可运行的协程，返回NULL
    if (runnable_count == 0) {
        return NULL;
    }

    // 简单的随机选择（使用静态变量模拟随机）
    static int rand_seed = 1;
    rand_seed = (rand_seed * 1103515245 + 12345) & 0x7fffffff;
    int choice = rand_seed % runnable_count;

    return runnable[choice];
}

// 实现co_start函数：创建新协程
struct co *co_start(const char *name, void (*func)(void *), void *arg) {
    // 第一次调用时初始化协程系统
    static int initialized = 0;
    if (!initialized) {
        co_init();
        initialized = 1;
    }

    // 查找空闲槽位
    int slot = find_free_slot();
    if (slot == -1) {
        // 没有空闲槽位，返回NULL
        return NULL;
    }

    // 分配协程结构体
    struct co *co = (struct co *)malloc(sizeof(struct co));
    if (!co) {
        return NULL;
    }

    // 分配协程栈空间
    co->stack = (char *)malloc(STACK_SIZE);
    if (!co->stack) {
        free(co);
        return NULL;
    }

    // 初始化协程结构体
    co->name = name;
    co->func = func;
    co->arg = arg;
    co->status = CO_NEW;
    co->waiter = NULL;

    // 设置协程栈指针（栈从高地址向低地址增长）
    // 在栈顶预留空间并设置返回地址为co_wrapper
    char *stack_top = co->stack + STACK_SIZE;

#if defined(__x86_64__)
    // x86-64架构：8字节对齐，预留返回地址空间
    stack_top -= sizeof(void *);
    *(void **)stack_top = (void *)co_wrapper;

    // 为寄存器保存预留空间（15个寄存器 * 8字节）
    stack_top -= 15 * sizeof(void *);
    co->sp = stack_top;
#elif defined(__i386__)
    // x86-32架构：4字节对齐，预留返回地址空间
    stack_top -= sizeof(void *);
    *(void **)stack_top = (void *)co_wrapper;

    // 为寄存器保存预留空间（7个寄存器 * 4字节）
    stack_top -= 7 * sizeof(void *);
    co->sp = stack_top;
#endif

    // 将协程加入列表
    co_list[slot] = co;
    co_count++;

    return co;
}

// 实现co_yield函数：协程主动让出CPU
void co_yield() {
    // 如果协程系统未初始化，先初始化
    if (current == NULL) {
        co_init();
        return;
    }

    // 选择下一个要运行的协程
    struct co *next = choose_next_co();

    // 如果没有其他可运行的协程，继续运行当前协程
    if (next == NULL || next == current) {
        return;
    }

    // 保存当前协程信息
    struct co *prev = current;
    current = next;

    // 如果目标协程是新创建的，标记为运行状态
    if (next->status == CO_NEW) {
        next->status = CO_RUNNING;
    }

    // 执行上下文切换
    co_switch(&prev->sp, next->sp);
}

// 实现co_wait函数：等待指定协程结束
void co_wait(struct co *co) {
    // 如果协程系统未初始化，先初始化
    if (current == NULL) {
        co_init();
    }

    // 如果协程已经结束，直接释放资源并返回
    if (co->status == CO_DEAD) {
        // 从协程列表中移除
        for (int i = 0; i < 128; i++) {
            if (co_list[i] == co) {
                co_list[i] = NULL;
                co_count--;
                break;
            }
        }

        // 释放协程资源
        free(co->stack);
        free(co);
        return;
    }

    // 设置当前协程为等待者
    co->waiter = current;
    current->status = CO_WAITING;

    // 持续让出CPU直到目标协程结束
    while (co->status != CO_DEAD) {
        co_yield();
    }

    // 协程已结束，从列表中移除并释放资源
    for (int i = 0; i < 128; i++) {
        if (co_list[i] == co) {
            co_list[i] = NULL;
            co_count--;
            break;
        }
    }

    // 释放协程资源
    free(co->stack);
    free(co);

    // 恢复当前协程状态
    current->status = CO_RUNNING;
}
